#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APP隐私合规检测工具安装脚本
自动配置环境和依赖
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                APP隐私合规检测工具 安装程序                    ║
    ║                Privacy Compliance Detector                   ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    
    if version < (3, 7):
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.7或更高版本")
        return False
    else:
        print(f"✅ Python版本符合要求: {version.major}.{version.minor}.{version.micro}")
        return True

def install_dependencies():
    """安装Python依赖"""
    print("\n📦 安装Python依赖包...")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Python依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def check_adb():
    """检查ADB环境"""
    print("\n🔍 检查ADB环境...")
    
    try:
        result = subprocess.run(["adb", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ADB环境正常")
            print(f"   版本信息: {result.stdout.split()[4]}")
            return True
        else:
            print("❌ ADB命令执行失败")
            return False
    except FileNotFoundError:
        print("❌ 未找到ADB命令")
        print("   请安装Android SDK Platform Tools")
        return False

def setup_frida_server():
    """设置Frida服务端"""
    print("\n🔧 配置Frida服务端...")
    
    # 检查设备连接
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
        devices = [line for line in result.stdout.split('\n') if '\tdevice' in line]
        
        if not devices:
            print("❌ 未检测到Android设备")
            print("   请确保设备已连接并启用USB调试")
            return False
        
        print(f"✅ 检测到 {len(devices)} 个设备")
        
        # 检查设备架构
        arch_result = subprocess.run(
            ["adb", "shell", "getprop", "ro.product.cpu.abi"], 
            capture_output=True, text=True
        )
        
        if arch_result.returncode == 0:
            arch = arch_result.stdout.strip()
            print(f"   设备架构: {arch}")
            
            # 提供Frida服务端下载建议
            frida_versions = {
                'arm64-v8a': 'frida-server-android-arm64',
                'armeabi-v7a': 'frida-server-android-arm',
                'x86_64': 'frida-server-android-x86_64',
                'x86': 'frida-server-android-x86'
            }
            
            if arch in frida_versions:
                server_name = frida_versions[arch]
                print(f"   建议下载: {server_name}")
                print(f"   下载地址: https://github.com/frida/frida/releases")
            
        return True
        
    except Exception as e:
        print(f"❌ Frida服务端配置失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录结构...")
    
    directories = [
        "reports",
        "logs",
        "logs/detection",
        "temp"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        except Exception as e:
            print(f"❌ 创建目录失败 {directory}: {e}")
            return False
    
    return True

def test_installation():
    """测试安装"""
    print("\n🧪 测试安装...")
    
    try:
        # 测试导入主要模块
        sys.path.insert(0, os.getcwd())
        
        from src.utils.logger import get_logger
        from src.utils.report_generator import ReportGenerator
        
        logger = get_logger("install_test")
        logger.info("安装测试成功")
        
        print("✅ 模块导入测试通过")
        
        # 测试报告生成
        generator = ReportGenerator("./reports")
        print("✅ 报告生成器初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 安装测试失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    guide = """
    🎉 安装完成！使用指南：
    
    1. GUI界面启动：
       python main.py --gui
    
    2. 命令行使用：
       python main.py --package com.example.app
    
    3. 运行测试：
       python test_system.py
    
    4. 查看帮助：
       python main.py --help
    
    📋 使用前准备：
    - 确保Android设备已连接
    - 启用USB调试模式
    - 安装并启动Frida服务端
    - 确保目标APP可调试或设备已Root
    
    📚 更多信息请查看 README.md
    """
    print(guide)

def main():
    """主安装流程"""
    print_banner()
    
    # 检查系统要求
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 安装失败：依赖包安装失败")
        sys.exit(1)
    
    # 检查ADB
    adb_ok = check_adb()
    if not adb_ok:
        print("\n⚠️  警告：ADB环境未配置，部分功能可能无法使用")
    
    # 配置Frida
    frida_ok = setup_frida_server()
    if not frida_ok:
        print("\n⚠️  警告：Frida环境未完全配置")
    
    # 创建目录
    if not create_directories():
        print("\n❌ 安装失败：目录创建失败")
        sys.exit(1)
    
    # 测试安装
    if not test_installation():
        print("\n❌ 安装失败：模块测试失败")
        sys.exit(1)
    
    # 显示使用指南
    show_usage_guide()
    
    print("\n🎉 安装完成！")

if __name__ == "__main__":
    main()
