#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动交互式检测测试
解决UI自动检测问题，改为手动确认
"""

import sys
import os
import time
import tkinter as tk
import tkinter.messagebox as msgbox
import json
from datetime import datetime

# 添加路径
sys.path.insert(0, '.')
sys.path.insert(0, 'src')

class ManualInteractiveDetector:
    """手动交互式检测器"""
    
    def __init__(self, package_name):
        self.package_name = package_name
        self.detection_active = True
        self.violations = []
        
    def run_detection(self):
        """运行手动交互式检测"""
        print(f"🚀 开始手动交互式检测: {self.package_name}")
        
        # 步骤1: 用户启动APP并确认隐私政策
        privacy_result = self._manual_privacy_check()
        
        if privacy_result['has_policy']:
            print("✅ 用户确认存在隐私政策")
            
            # 步骤2: 用户选择同意或不同意
            user_choice = self._get_user_choice()
            
            if user_choice == 'agree':
                print("👍 用户选择同意，开始同意后监控...")
                return self._monitor_after_agreement()
            elif user_choice == 'disagree':
                print("👎 用户选择不同意，开始违规监控...")
                return self._monitor_after_disagreement()
            else:
                print("⏭️ 用户跳过检测")
                return {'skipped': True, 'reason': '用户跳过'}
        else:
            print("❌ 用户确认没有隐私政策")
            return self._generate_no_policy_report()
    
    def _manual_privacy_check(self):
        """手动检查隐私政策"""
        # 指导用户启动APP
        msgbox.showinfo(
            "🎯 步骤1: 启动APP",
            f"📱 目标APP: {self.package_name}\n\n"
            "请按照以下步骤操作：\n\n"
            "1️⃣ 手动启动目标APP\n"
            "2️⃣ 观察APP启动过程\n"
            "3️⃣ 查看是否出现隐私政策相关弹窗\n\n"
            "⚠️ 如果出现隐私政策弹窗，请不要点击任何按钮！\n\n"
            "点击确定继续..."
        )
        
        # 给用户时间启动APP
        time.sleep(2)
        
        # 询问用户是否看到隐私政策
        has_policy = msgbox.askyesno(
            "🔍 隐私政策检查",
            "请仔细观察APP界面：\n\n"
            "您是否看到了隐私政策相关的弹窗或提示？\n\n"
            "隐私政策可能包含以下内容：\n"
            "• 隐私政策、隐私保护、隐私协议\n"
            "• 用户协议、服务协议、使用条款\n"
            "• 个人信息收集、数据处理说明\n"
            "• 第三方SDK清单\n"
            "• 同意/不同意按钮\n\n"
            "您看到了隐私政策弹窗吗？"
        )
        
        if has_policy:
            # 让用户描述看到的内容
            content_description = self._get_policy_description()
            return {
                'has_policy': True,
                'user_confirmed': True,
                'description': content_description,
                'timestamp': time.time()
            }
        else:
            # 再次确认
            double_check = msgbox.askyesno(
                "🔍 再次确认",
                "请再次仔细检查：\n\n"
                "• APP是否已经完全启动？\n"
                "• 是否有任何弹窗或对话框？\n"
                "• 是否有关于隐私、协议的文字？\n\n"
                "确认没有看到任何隐私政策相关内容吗？"
            )
            
            return {
                'has_policy': not double_check,
                'user_confirmed': True,
                'timestamp': time.time()
            }
    
    def _get_policy_description(self):
        """获取用户对隐私政策的描述"""
        # 创建一个简单的输入对话框
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        description = tk.simpledialog.askstring(
            "📝 描述隐私政策内容",
            "请简单描述您看到的隐私政策内容：\n\n"
            "例如：\n"
            "• 弹窗标题是什么？\n"
            "• 有哪些按钮？\n"
            "• 主要内容是什么？\n\n"
            "请输入描述："
        )
        
        root.destroy()
        return description or "用户未提供描述"
    
    def _get_user_choice(self):
        """获取用户在APP中的选择"""
        msgbox.showinfo(
            "📋 步骤2: 用户选择",
            "✅ 已确认存在隐私政策！\n\n"
            "现在请在APP中进行选择：\n\n"
            "• 仔细阅读隐私政策内容\n"
            "• 根据您的意愿选择'同意'或'不同意'\n"
            "• 完成选择后回到此对话框\n\n"
            "⚠️ 请先在APP中操作，再点击下一步！"
        )
        
        while True:
            choice = msgbox.askyesnocancel(
                "🔍 确认您的选择",
                "请告诉检测器您在APP中的选择：\n\n"
                "• 点击'是'：我选择了'同意/接受'\n"
                "• 点击'否'：我选择了'不同意/拒绝'\n"
                "• 点击'取消'：我需要更多时间或跳过\n\n"
                "您在APP中选择了什么？"
            )
            
            if choice is True:
                return 'agree'
            elif choice is False:
                return 'disagree'
            else:
                skip = msgbox.askyesno(
                    "跳过检测？",
                    "是否跳过此次检测？\n\n"
                    "点击'是'跳过，点击'否'继续等待。"
                )
                if skip:
                    return 'skip'
                else:
                    msgbox.showinfo("继续等待", "请继续在APP中操作...")
    
    def _monitor_after_agreement(self):
        """用户同意后的监控"""
        start_time = time.time()
        
        msgbox.showinfo(
            "🔄 同意后监控",
            "用户已同意隐私政策！\n\n"
            "检测器将监控以下行为：\n"
            "• 权限请求是否合理\n"
            "• 数据收集是否符合政策\n"
            "• 第三方SDK行为\n"
            "• 功能访问路径深度\n\n"
            "请正常使用APP 30秒，检测器会在后台监控。\n"
            "点击确定开始监控..."
        )
        
        # 模拟30秒监控
        for i in range(30):
            if not self.detection_active:
                break
            
            print(f"[同意后监控] {i+1}/30 秒...")
            
            # 每10秒检查一次权限请求
            if (i + 1) % 10 == 0:
                permission_check = self._check_permission_requests()
                if permission_check:
                    self.violations.extend(permission_check)
            
            time.sleep(1)
        
        duration = time.time() - start_time
        
        msgbox.showinfo(
            "✅ 同意后监控完成",
            f"监控已完成！\n\n"
            f"监控时长: {duration:.1f} 秒\n"
            f"发现问题: {len(self.violations)} 个\n\n"
            f"检测报告已生成。"
        )
        
        return self._generate_report('agreement_monitoring', duration)
    
    def _monitor_after_disagreement(self):
        """用户拒绝后的监控"""
        start_time = time.time()
        
        msgbox.showinfo(
            "🚨 拒绝后违规监控",
            "用户已拒绝隐私政策！\n\n"
            "检测器将监控违规行为：\n"
            "• 未经同意收集数据\n"
            "• 强制要求权限\n"
            "• 限制APP功能\n"
            "• 违规网络请求\n\n"
            "请继续使用APP 30秒，检测器会记录违规行为。\n"
            "点击确定开始监控..."
        )
        
        # 模拟30秒违规监控
        for i in range(30):
            if not self.detection_active:
                break
            
            print(f"[违规监控] {i+1}/30 秒...")
            
            # 每5秒检查一次违规行为
            if (i + 1) % 5 == 0:
                violation_check = self._check_violations_after_disagreement()
                if violation_check:
                    self.violations.extend(violation_check)
            
            time.sleep(1)
        
        duration = time.time() - start_time
        
        msgbox.showinfo(
            "🚨 违规监控完成",
            f"违规监控已完成！\n\n"
            f"监控时长: {duration:.1f} 秒\n"
            f"发现违规: {len(self.violations)} 个\n\n"
            f"违规报告已生成。"
        )
        
        return self._generate_report('violation_monitoring', duration)
    
    def _check_permission_requests(self):
        """检查权限请求（模拟）"""
        # 询问用户是否看到权限请求
        has_permission_request = msgbox.askyesno(
            "🔍 权限请求检查",
            "在刚才的使用过程中，您是否看到了权限请求弹窗？\n\n"
            "例如：\n"
            "• 位置权限请求\n"
            "• 相机权限请求\n"
            "• 联系人权限请求\n"
            "• 存储权限请求\n\n"
            "看到了权限请求吗？"
        )
        
        if has_permission_request:
            # 这里可以添加更详细的权限检查逻辑
            return [{
                'type': 'permission_request_detected',
                'description': '检测到权限请求，需要验证是否合理',
                'timestamp': time.time(),
                'severity': 'medium'
            }]
        
        return []
    
    def _check_violations_after_disagreement(self):
        """检查拒绝后的违规行为（模拟）"""
        # 询问用户APP是否有异常行为
        has_violation = msgbox.askyesno(
            "🚨 违规行为检查",
            "在拒绝隐私政策后，APP是否有以下异常行为？\n\n"
            "• 强制退出或无法使用\n"
            "• 仍然弹出权限请求\n"
            "• 功能被限制\n"
            "• 显示错误信息\n\n"
            "发现异常行为了吗？"
        )
        
        if has_violation:
            return [{
                'type': 'violation_after_disagreement',
                'description': '用户拒绝隐私政策后APP出现异常行为',
                'timestamp': time.time(),
                'severity': 'high'
            }]
        
        return []
    
    def _generate_no_policy_report(self):
        """生成无隐私政策的报告"""
        return {
            'success': True,
            'mode': 'no_privacy_policy',
            'violations': [{
                'type': 'missing_privacy_policy',
                'description': 'APP启动时未显示隐私政策',
                'severity': 'high',
                'timestamp': time.time()
            }],
            'recommendations': [
                '在APP首次启动时显示隐私政策弹窗',
                '确保用户能够清楚了解数据收集和使用情况'
            ]
        }
    
    def _generate_report(self, mode, duration):
        """生成检测报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report = {
            'detection_info': {
                'package_name': self.package_name,
                'detection_mode': f'manual_interactive_{mode}',
                'timestamp': timestamp,
                'duration': duration
            },
            'summary': {
                'total_violations': len(self.violations),
                'detection_success': True,
                'mode': mode
            },
            'violations': self.violations,
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        report_filename = f"manual_interactive_report_{self.package_name}_{timestamp}.json"
        os.makedirs("./reports", exist_ok=True)
        report_path = os.path.join("./reports", report_filename)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        report['report_path'] = report_path
        return report
    
    def _generate_recommendations(self):
        """生成改进建议"""
        recommendations = []
        
        for violation in self.violations:
            if violation['type'] == 'permission_request_detected':
                recommendations.append('确保权限请求有明确的使用说明')
            elif violation['type'] == 'violation_after_disagreement':
                recommendations.append('尊重用户选择，不要在用户拒绝后限制功能')
        
        return recommendations

def main():
    """主函数"""
    print("🧪 手动交互式检测测试")
    print("=" * 50)
    
    package_name = "com.ivy.qtbz"
    
    print(f"📱 目标APP: {package_name}")
    print("🎯 这是真正的交互式检测，会等待您的操作")
    print("💡 解决了自动UI检测的问题，改为手动确认")
    
    detector = ManualInteractiveDetector(package_name)
    
    try:
        result = detector.run_detection()
        
        print("\n📊 检测结果:")
        print("=" * 30)
        
        if result.get('success'):
            print(f"✅ 检测成功")
            print(f"🔧 检测模式: {result.get('mode', 'unknown')}")
            print(f"⏱️ 检测时长: {result.get('duration', 0):.1f} 秒")
            print(f"🚨 发现问题: {len(result.get('violations', []))} 个")
            print(f"📄 报告路径: {result.get('report_path', 'N/A')}")
        elif result.get('skipped'):
            print(f"⏭️ 检测被跳过: {result.get('reason', '未知原因')}")
        else:
            print(f"❌ 检测失败")
            
    except KeyboardInterrupt:
        print("\n⏹️ 检测被用户中断")
        detector.detection_active = False
    except Exception as e:
        print(f"\n❌ 检测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
