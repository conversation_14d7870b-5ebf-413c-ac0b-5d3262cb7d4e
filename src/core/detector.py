#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隐私合规检测核心模块
"""

import os
import time
import json
import frida
import threading
from typing import Dict, Any, List, Optional
from ..utils.logger import get_logger, DetectionLogger
from ..utils.report_generator import ReportGenerator
from ..detectors.privacy_policy_detector import PrivacyPolicyDetector
from ..detectors.permission_detector import PermissionDetector
from ..detectors.sdk_detector import SDKDetector
from ..detectors.user_rights_detector import UserRightsDetector

logger = get_logger(__name__)

class PrivacyComplianceDetector:
    """隐私合规检测主控制器"""
    
    def __init__(self, package_name: str, device_id: Optional[str] = None, 
                 output_dir: str = "./reports", config_path: str = "./config/config.json"):
        self.package_name = package_name
        self.device_id = device_id
        self.output_dir = output_dir
        self.config_path = config_path
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化组件
        self.device = None
        self.session = None
        self.script = None
        self.detection_logger = None
        self.report_generator = ReportGenerator(output_dir)
        
        # 检测结果
        self.detection_results = {
            'privacy_policy_violations': [],
            'permission_violations': [],
            'sdk_violations': [],
            'user_rights_violations': [],
            'raw_data': {
                'permission_requests': [],
                'data_collections': [],
                'network_requests': [],
                'ui_events': [],
                'sdk_activities': []
            }
        }
        
        # 检测状态
        self.detection_active = False
        self.start_time = None
        self.app_info = {}
        
        # 事件处理
        self._setup_event_handlers()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"配置文件加载成功: {self.config_path}")
            return config
        except Exception as e:
            logger.warning(f"配置文件加载失败，使用默认配置: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "detection_settings": {
                "timeout": 300,
                "max_violations_per_type": 100,
                "enable_screenshot": True,
                "enable_network_monitoring": True,
                "enable_file_monitoring": True
            },
            "frida_settings": {
                "spawn_timeout": 10,
                "attach_timeout": 5,
                "script_timeout": 30
            },
            "report_settings": {
                "format": "xlsx",
                "include_screenshots": True,
                "include_call_stack": True,
                "include_timeline": True,
                "language": "zh-CN"
            }
        }
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        self.event_handlers = {
            'log': self._handle_log_event,
            'violation': self._handle_violation_event,
            'ui_log': self._handle_ui_event,
            'ui_violation': self._handle_ui_violation,
            'sdk_log': self._handle_sdk_event,
            'sdk_violation': self._handle_sdk_violation
        }
    
    def run_detection(self) -> Dict[str, Any]:
        """运行完整的隐私合规检测"""
        logger.info(f"开始检测APP: {self.package_name}")
        self.start_time = time.time()
        
        try:
            # 1. 连接设备和APP
            self._connect_to_device()
            self._attach_to_app()
            
            # 2. 初始化检测日志
            session_id = str(int(time.time()))
            self.detection_logger = DetectionLogger(self.package_name, session_id)
            
            # 3. 获取APP信息
            self._collect_app_info()
            
            # 4. 加载Frida脚本
            self._load_frida_scripts()
            
            # 5. 执行检测
            self._run_detection_phases()
            
            # 6. 生成报告
            report_path = self._generate_report()
            
            # 7. 清理资源
            self._cleanup()
            
            detection_duration = time.time() - self.start_time
            logger.info(f"检测完成，耗时: {detection_duration:.1f}秒")
            
            return {
                'success': True,
                'report_path': report_path,
                'detection_duration': detection_duration,
                'violations_count': self._count_total_violations(),
                'app_info': self.app_info
            }
            
        except Exception as e:
            logger.error(f"检测过程中发生错误: {e}")
            self._cleanup()
            return {
                'success': False,
                'error': str(e),
                'detection_duration': time.time() - self.start_time if self.start_time else 0
            }
    
    def _connect_to_device(self):
        """连接到设备"""
        try:
            if self.device_id:
                self.device = frida.get_device(self.device_id)
            else:
                # 获取设备列表并优先选择USB设备
                devices = frida.enumerate_devices()
                logger.info(f"发现设备: {[(d.name, d.type, d.id) for d in devices]}")

                # 优先选择USB设备
                usb_devices = [d for d in devices if d.type == 'usb']
                if usb_devices:
                    self.device = usb_devices[0]
                    logger.info(f"选择USB设备: {self.device.name}")
                else:
                    # 如果没有USB设备，尝试使用frida.get_usb_device()
                    try:
                        self.device = frida.get_usb_device()
                        logger.info(f"通过get_usb_device()获取设备: {self.device.name}")
                    except:
                        # 最后尝试remote设备
                        remote_devices = [d for d in devices if d.type == 'remote']
                        if remote_devices:
                            # 过滤掉Local Socket和GDB，选择真正的远程设备
                            real_remote = [d for d in remote_devices if 'Local Socket' not in d.name and 'GDB' not in d.name]
                            if real_remote:
                                self.device = real_remote[0]
                            else:
                                raise Exception("未找到可用的设备，请确保设备已连接并启动Frida服务端")
                        else:
                            raise Exception("未找到可用的USB或远程设备")

            logger.info(f"已连接到设备: {self.device.name} ({self.device.type})")

        except Exception as e:
            raise Exception(f"设备连接失败: {e}")
    
    def _attach_to_app(self):
        """附加到目标APP"""
        try:
            # 检查APP是否正在运行
            processes = self.device.enumerate_processes()
            target_processes = [p for p in processes if p.name == self.package_name]

            if target_processes:
                # 附加到正在运行的进程
                target_process = target_processes[0]
                logger.info(f"发现运行中的进程: {target_process.name} (PID: {target_process.pid})")

                # 尝试附加，增加重试机制
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        self.session = self.device.attach(target_process.pid)
                        logger.info(f"已附加到进程: {target_process.pid}")
                        break
                    except Exception as e:
                        logger.warning(f"附加尝试 {attempt + 1} 失败: {e}")
                        if attempt == max_retries - 1:
                            raise e
                        time.sleep(1)
            else:
                # 启动APP并附加
                logger.info(f"APP未运行，尝试启动: {self.package_name}")
                pid = self.device.spawn([self.package_name])
                self.session = self.device.attach(pid)
                self.device.resume(pid)
                logger.info(f"已启动并附加到APP: {self.package_name} (PID: {pid})")

        except Exception as e:
            raise Exception(f"APP附加失败: {e}")
    
    def _collect_app_info(self):
        """收集APP信息"""
        try:
            # 获取APP基本信息
            self.app_info = {
                'package_name': self.package_name,
                'device_info': f"{self.device.name} ({self.device.type})",
                'detection_start_time': self.start_time,
                'app_name': self.package_name.split('.')[-1],  # 简化的APP名称
                'version': 'unknown'  # 需要通过其他方式获取
            }
            
            logger.info(f"APP信息收集完成: {self.app_info}")
            
        except Exception as e:
            logger.warning(f"APP信息收集失败: {e}")
    
    def _load_frida_scripts(self):
        """加载Frida脚本"""
        try:
            # 读取核心Hook脚本
            script_dir = os.path.join(os.path.dirname(__file__), '..', 'frida_scripts')
            
            scripts = [
                'core_hooks.js',
                'ui_hooks.js', 
                'sdk_hooks.js'
            ]
            
            combined_script = ""
            for script_name in scripts:
                script_path = os.path.join(script_dir, script_name)
                if os.path.exists(script_path):
                    with open(script_path, 'r', encoding='utf-8') as f:
                        combined_script += f"\n// === {script_name} ===\n"
                        combined_script += f.read()
                        combined_script += "\n"
            
            # 创建并加载脚本
            self.script = self.session.create_script(combined_script)
            self.script.on('message', self._on_message)
            self.script.load()
            
            logger.info("Frida脚本加载成功")
            
        except Exception as e:
            raise Exception(f"Frida脚本加载失败: {e}")
    
    def _on_message(self, message, data):
        """处理Frida脚本消息"""
        try:
            if message['type'] == 'send':
                payload = message['payload']
                event_type = payload.get('type')
                
                if event_type in self.event_handlers:
                    self.event_handlers[event_type](payload)
                else:
                    logger.debug(f"未处理的消息类型: {event_type}")
            
        except Exception as e:
            logger.error(f"消息处理错误: {e}")
    
    def _handle_log_event(self, payload):
        """处理日志事件"""
        log_data = payload.get('payload', {})
        level = log_data.get('level', 'info')
        message = log_data.get('message', '')
        
        if level == 'error':
            logger.error(f"Frida: {message}")
        elif level == 'warning':
            logger.warning(f"Frida: {message}")
        else:
            logger.debug(f"Frida: {message}")
    
    def _handle_violation_event(self, payload):
        """处理违规事件"""
        violation = payload.get('payload', {})
        self.detection_results['permission_violations'].append(violation)
        
        if self.detection_logger:
            self.detection_logger.log_violation(
                violation.get('type', ''),
                violation.get('description', ''),
                violation.get('evidence', {})
            )
    
    def _handle_ui_event(self, payload):
        """处理UI事件"""
        ui_data = payload.get('payload', {})
        self.detection_results['raw_data']['ui_events'].append(ui_data)
    
    def _handle_ui_violation(self, payload):
        """处理UI违规事件"""
        violation = payload.get('payload', {})
        self.detection_results['privacy_policy_violations'].append(violation)
    
    def _handle_sdk_event(self, payload):
        """处理SDK事件"""
        sdk_data = payload.get('payload', {})
        self.detection_results['raw_data']['sdk_activities'].append(sdk_data)
    
    def _handle_sdk_violation(self, payload):
        """处理SDK违规事件"""
        violation = payload.get('payload', {})
        self.detection_results['sdk_violations'].append(violation)
    
    def _run_detection_phases(self):
        """运行交互式检测阶段"""
        logger.info("开始执行交互式检测阶段")

        # 启动交互式检测器
        from .interactive_detector import InteractivePrivacyDetector
        from ..utils.ui_analyzer import UIAnalyzer

        ui_analyzer = UIAnalyzer(self.device_id)
        interactive_detector = InteractivePrivacyDetector(
            self.session, self.device, self.config, ui_analyzer
        )

        # 设置回调函数
        interactive_detector.on_violation_found = self._on_interactive_violation
        interactive_detector.on_user_action_needed = self._on_user_action_needed
        interactive_detector.on_progress_update = self._on_progress_update

        # 开始交互式检测
        interactive_detector.start_interactive_detection()

        # 获取检测结果
        interactive_results = interactive_detector.get_detection_results()

        # 合并结果
        self._merge_interactive_results(interactive_results)

    def _on_interactive_violation(self, violation_data):
        """处理交互式检测发现的违规"""
        violation_type = violation_data.get('category', 'unknown')

        if violation_type == 'privacy_policy':
            self.detection_results['privacy_policy_violations'].append(violation_data)
        elif violation_type == 'permission':
            self.detection_results['permission_violations'].append(violation_data)
        elif violation_type == 'sdk':
            self.detection_results['sdk_violations'].append(violation_data)
        elif violation_type == 'user_rights':
            self.detection_results['user_rights_violations'].append(violation_data)

        logger.warning(f"发现违规: {violation_data.get('type', 'unknown')} - {violation_data.get('description', '')}")

    def _on_user_action_needed(self, action_info):
        """处理需要用户操作的情况"""
        action_type = action_info.get('action_type')
        message = action_info.get('message', '')

        print(f"\n{'='*60}")
        print(f"需要用户操作: {action_type}")
        print(f"说明: {message}")
        print(f"{'='*60}")

        if action_type == 'privacy_policy_choice':
            print("请在APP中选择:")
            print("1. 点击'同意'按钮")
            print("2. 点击'不同意'按钮")
            print("3. 输入'skip'跳过此检测")
        elif action_type == 'permission_request':
            print("检测到权限请求弹窗，请选择:")
            print("1. 点击'允许'")
            print("2. 点击'拒绝'")
            print("3. 输入'skip'跳过此检测")

        # 等待用户操作
        user_input = input("\n请输入您的选择 (或输入'continue'继续检测): ").strip().lower()
        return user_input

    def _on_progress_update(self, progress_info):
        """处理进度更新"""
        stage = progress_info.get('stage', '')
        progress = progress_info.get('progress', 0)
        message = progress_info.get('message', '')

        print(f"[{stage}] {progress}% - {message}")

    def _merge_interactive_results(self, interactive_results):
        """合并交互式检测结果"""
        if not interactive_results:
            return

        for category, violations in interactive_results.items():
            if category in self.detection_results and isinstance(violations, list):
                self.detection_results[category].extend(violations)

    def _run_privacy_policy_detection(self):
        """运行隐私政策检测"""
        try:
            detector = PrivacyPolicyDetector(self.session, self.device)
            violations = detector.detect_all()
            self.detection_results['privacy_policy_violations'].extend(violations)
            logger.info(f"隐私政策检测完成，发现 {len(violations)} 个违规项")
        except Exception as e:
            logger.error(f"隐私政策检测失败: {e}")
    
    def _run_permission_detection(self):
        """运行权限检测"""
        try:
            detector = PermissionDetector(self.session, self.device, self.config)
            violations = detector.detect_all()
            self.detection_results['permission_violations'].extend(violations)
            logger.info(f"权限检测完成，发现 {len(violations)} 个违规项")
        except Exception as e:
            logger.error(f"权限检测失败: {e}")
    
    def _run_sdk_detection(self):
        """运行SDK检测"""
        try:
            detector = SDKDetector(self.session, self.device, self.config)
            violations = detector.detect_all()
            self.detection_results['sdk_violations'].extend(violations)
            logger.info(f"SDK检测完成，发现 {len(violations)} 个违规项")
        except Exception as e:
            logger.error(f"SDK检测失败: {e}")
    
    def _run_user_rights_detection(self):
        """运行用户权利检测"""
        try:
            detector = UserRightsDetector(self.session, self.device, self.config)
            violations = detector.detect_all()
            self.detection_results['user_rights_violations'].extend(violations)
            logger.info(f"用户权利检测完成，发现 {len(violations)} 个违规项")
        except Exception as e:
            logger.error(f"用户权利检测失败: {e}")
    
    def _generate_report(self) -> str:
        """生成检测报告"""
        try:
            self.app_info['detection_duration'] = time.time() - self.start_time
            report_path = self.report_generator.generate_report(
                self.detection_results, 
                self.app_info
            )
            logger.info(f"检测报告已生成: {report_path}")
            return report_path
        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            return ""
    
    def _count_total_violations(self) -> int:
        """统计总违规数量"""
        total = 0
        for violations in self.detection_results.values():
            if isinstance(violations, list):
                total += len(violations)
        return total
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.script:
                self.script.unload()
            if self.session:
                self.session.detach()
            logger.info("资源清理完成")
        except Exception as e:
            logger.warning(f"资源清理时发生错误: {e}")
