#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式隐私合规检测器
支持根据用户操作进行动态检测，大幅减少误报
"""

import time
import re
import json
import threading
import subprocess
from typing import Dict, Any, List, Optional, Callable
from ..utils.logger import get_logger

logger = get_logger(__name__)

class InteractivePrivacyDetector:
    """交互式隐私合规检测器"""

    def __init__(self, frida_session, device, config, ui_analyzer=None):
        self.session = frida_session
        self.device = device
        self.config = config
        self.ui_analyzer = ui_analyzer
        self.violations = []

        # 检测状态
        self.detection_active = True
        self.user_consent_given = None  # None=未选择, True=同意, False=不同意
        self.privacy_policy_shown = False
        self.privacy_policy_content = ""
        self.current_ui_text = ""
        self.app_package = None

        # 事件记录
        self.ui_events = []
        self.permission_requests = []
        self.data_collections = []
        self.user_interactions = []
        self.ui_state_history = []

        # 回调函数
        self.on_violation_found: Optional[Callable] = None
        self.on_user_action_needed: Optional[Callable] = None
        self.on_progress_update: Optional[Callable] = None

        # 监控线程
        self.monitor_thread = None
        self.stop_monitoring = False
        
    def start_interactive_detection(self):
        """开始交互式检测"""
        logger.info("开始交互式隐私合规检测")

        try:
            # 1. 检测APP启动时的隐私政策弹窗
            self._detect_startup_privacy_policy()

            # 2. 根据用户选择进行不同的检测路径
            if self.user_consent_given is True:
                self._detect_post_consent_behavior()
            elif self.user_consent_given is False:
                self._detect_pre_consent_violations()
            else:
                # 用户未做选择或跳过，进行基础检测
                self._detect_basic_violations()

            # 3. 启动持续监控
            self._start_continuous_monitoring()

        except Exception as e:
            logger.error(f"交互式检测过程中发生错误: {e}")

        return self.get_detection_results()

    def _detect_startup_privacy_policy(self):
        """检测APP启动时的隐私政策"""
        self._update_progress("隐私政策检测", 10, "检测APP启动时的隐私政策显示...")

        # 等待APP完全启动
        time.sleep(3)

        # 获取当前UI状态
        if self.ui_analyzer:
            ui_state = self.ui_analyzer.get_current_ui_state()
            self.ui_state_history.append({
                'timestamp': time.time(),
                'stage': 'startup',
                'ui_state': ui_state
            })

            # 检测隐私政策弹窗
            policy_detection = self.ui_analyzer.detect_privacy_policy_popup(ui_state)

            if policy_detection['detected']:
                self.privacy_policy_shown = True
                logger.info(f"检测到隐私政策弹窗 (置信度: {policy_detection['confidence']}%)")

                # 分析隐私政策内容质量
                content_analysis = self.ui_analyzer.analyze_privacy_policy_content(ui_state)

                # 检查内容质量问题
                if content_analysis['quality_score'] < 60:
                    self._add_violation('privacy_policy', {
                        'type': 'poor_privacy_policy_quality',
                        'description': f'隐私政策内容质量较低 (评分: {content_analysis["quality_score"]:.1f}/100)',
                        'severity': 'medium',
                        'evidence': {
                            'quality_score': content_analysis['quality_score'],
                            'missing_elements': content_analysis['missing_elements'],
                            'text_length': content_analysis['text_length']
                        },
                        'recommendation': f'完善隐私政策内容，补充缺失的: {", ".join(content_analysis["missing_elements"])}'
                    })

                # 等待用户操作
                self._wait_for_privacy_policy_choice(ui_state)

            else:
                # 未检测到隐私政策弹窗
                self._add_violation('privacy_policy', {
                    'type': 'missing_privacy_policy_popup',
                    'description': 'APP启动时未显示隐私政策弹窗',
                    'severity': 'high',
                    'evidence': {
                        'ui_text_sample': ui_state.get('full_text', '')[:200],
                        'detection_confidence': policy_detection['confidence']
                    },
                    'recommendation': '在APP首次启动时显示隐私政策同意弹窗'
                })
        else:
            # 使用传统方法检测
            self._monitor_app_startup()
            self._wait_for_user_consent()

    def _wait_for_privacy_policy_choice(self, ui_state):
        """等待用户对隐私政策的选择"""
        if self.on_user_action_needed:
            print(f"\n{'='*60}")
            print("检测到隐私政策弹窗！")
            print("请在APP中选择您的操作:")
            print("1. 点击'同意'按钮")
            print("2. 点击'不同意'按钮")
            print("3. 输入'skip'跳过此检测")
            print(f"{'='*60}")

            user_input = input("请输入您的选择 (agree/disagree/skip): ").strip().lower()

            if user_input in ['agree', '同意', '1']:
                self.user_consent_given = True
                logger.info("用户选择同意隐私政策")
            elif user_input in ['disagree', '不同意', '2']:
                self.user_consent_given = False
                logger.info("用户选择不同意隐私政策")
            elif user_input == 'skip':
                self.user_consent_given = None
                logger.info("用户跳过隐私政策选择")
            else:
                # 尝试自动检测用户选择
                self.user_consent_given = self._auto_detect_user_choice()
        else:
            # 自动检测用户选择
            self.user_consent_given = self._auto_detect_user_choice()

    def _auto_detect_user_choice(self) -> Optional[bool]:
        """自动检测用户选择"""
        # 监控UI变化来推断用户选择
        start_time = time.time()
        timeout = 60  # 60秒超时

        while time.time() - start_time < timeout:
            if self.ui_analyzer:
                current_ui = self.ui_analyzer.get_current_ui_state()

                # 检测是否还有隐私政策弹窗
                policy_detection = self.ui_analyzer.detect_privacy_policy_popup(current_ui)

                if not policy_detection['detected']:
                    # 弹窗消失，尝试判断用户选择
                    return self._infer_choice_from_ui_change(current_ui)

            time.sleep(2)

        logger.warning("自动检测用户隐私政策选择超时")
        return None

    def _infer_choice_from_ui_change(self, current_ui) -> Optional[bool]:
        """从UI变化推断用户选择"""
        # 检查是否进入主界面（表示用户同意）
        full_text = current_ui.get('full_text', '').lower()

        # 主界面指示词
        main_ui_indicators = [
            '首页', '主页', '主界面', '菜单', '导航', '设置',
            'home', 'main', 'menu', 'navigation', 'settings'
        ]

        # 退出/关闭指示词
        exit_indicators = [
            '退出', '关闭', '结束', 'exit', 'close', 'quit'
        ]

        if any(indicator in full_text for indicator in main_ui_indicators):
            logger.info("检测到用户可能同意了隐私政策（进入主界面）")
            return True
        elif any(indicator in full_text for indicator in exit_indicators):
            logger.info("检测到用户可能拒绝了隐私政策（APP退出）")
            return False

        return None

    def _detect_post_consent_behavior(self):
        """检测用户同意隐私政策后的行为"""
        self._update_progress("同意后检测", 40, "检测用户同意隐私政策后的行为...")

        # 启动权限监控
        self._start_permission_monitoring()

        # 检测权限请求是否合理
        self._monitor_reasonable_permission_requests()

    def _detect_pre_consent_violations(self):
        """检测用户拒绝隐私政策后的违规行为"""
        self._update_progress("拒绝后检测", 40, "检测用户拒绝隐私政策后的违规行为...")

        # 监控是否在用户拒绝后仍然收集数据
        violations = self._detect_unauthorized_data_collection()

        for violation in violations:
            self._add_violation('permission', violation)

    def _detect_basic_violations(self):
        """基础违规检测（用户未做选择时）"""
        self._update_progress("基础检测", 40, "执行基础违规检测...")

        # 执行基本的权限和数据收集检测
        self._start_permission_monitoring()

    def _start_permission_monitoring(self):
        """启动权限监控"""
        logger.info("启动权限监控")

        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._permission_monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def _permission_monitor_loop(self):
        """权限监控循环"""
        while not self.stop_monitoring and self.detection_active:
            try:
                if self.ui_analyzer:
                    current_ui = self.ui_analyzer.get_current_ui_state()

                    # 检测权限请求
                    permission_detection = self.ui_analyzer.detect_permission_request(current_ui)

                    if permission_detection['detected']:
                        self._handle_permission_request(permission_detection, current_ui)

                time.sleep(2)

            except Exception as e:
                logger.error(f"权限监控过程中发生错误: {e}")
                time.sleep(5)

    def _handle_permission_request(self, permission_detection, ui_state):
        """处理权限请求"""
        permission_types = permission_detection['permission_types']
        logger.info(f"检测到权限请求: {', '.join(permission_types)}")

        # 记录权限请求
        permission_request = {
            'permission_types': permission_types,
            'timestamp': time.time(),
            'ui_state': ui_state,
            'user_consent_status': self.user_consent_given,
            'user_choice': None
        }

        self.permission_requests.append(permission_request)

        # 检查是否为违规请求
        if self.user_consent_given is False:
            # 用户拒绝隐私政策后仍然请求权限
            self._add_violation('permission', {
                'type': 'unauthorized_permission_request_after_refusal',
                'description': f'用户拒绝隐私政策后仍然请求{", ".join(permission_types)}权限',
                'severity': 'high',
                'evidence': {
                    'permission_types': permission_types,
                    'request_time': time.time(),
                    'user_consent_status': 'refused'
                },
                'recommendation': '应在用户同意隐私政策后再请求相关权限'
            })

        # 通知用户需要操作
        if self.on_user_action_needed:
            print(f"\n{'='*60}")
            print(f"检测到权限请求: {', '.join(permission_types)}")
            print("请在APP中选择:")
            print("1. 点击'允许'")
            print("2. 点击'拒绝'")
            print("3. 输入'skip'跳过此检测")
            print(f"{'='*60}")

            user_input = input("请输入您的选择 (allow/deny/skip): ").strip().lower()
            permission_request['user_choice'] = user_input

            if user_input in ['deny', '拒绝', '2']:
                # 用户拒绝权限，监控是否仍然收集数据
                self._monitor_post_denial_behavior(permission_types)

    def _monitor_post_denial_behavior(self, denied_permissions):
        """监控用户拒绝权限后的行为"""
        logger.info(f"监控用户拒绝{', '.join(denied_permissions)}权限后的行为")

        # 监控一段时间，检测是否仍然尝试收集数据
        monitoring_duration = 30  # 30秒
        start_time = time.time()

        while time.time() - start_time < monitoring_duration:
            # 这里应该通过Frida Hook检测实际的数据收集行为
            # 简化实现：检测是否再次弹出权限请求
            if self.ui_analyzer:
                current_ui = self.ui_analyzer.get_current_ui_state()
                permission_detection = self.ui_analyzer.detect_permission_request(current_ui)

                if permission_detection['detected']:
                    # 检查是否为相同权限的重复请求
                    detected_types = permission_detection['permission_types']
                    repeated_permissions = [p for p in detected_types if p in denied_permissions]

                    if repeated_permissions:
                        self._add_violation('permission', {
                            'type': 'repeated_permission_request_after_denial',
                            'description': f'用户拒绝后重复请求{", ".join(repeated_permissions)}权限',
                            'severity': 'medium',
                            'evidence': {
                                'denied_permissions': denied_permissions,
                                'repeated_permissions': repeated_permissions,
                                'time_since_denial': time.time() - start_time
                            },
                            'recommendation': '尊重用户选择，避免重复请求已被拒绝的权限'
                        })

            time.sleep(3)

    def _detect_unauthorized_data_collection(self) -> List[Dict[str, Any]]:
        """检测未授权的数据收集"""
        violations = []

        # 监控一段时间内的权限请求和数据访问
        monitoring_duration = 30  # 30秒
        start_time = time.time()

        logger.info("开始监控未授权数据收集行为")

        while time.time() - start_time < monitoring_duration:
            if self.ui_analyzer:
                current_ui = self.ui_analyzer.get_current_ui_state()
                permission_detection = self.ui_analyzer.detect_permission_request(current_ui)

                if permission_detection['detected']:
                    violations.append({
                        'type': 'unauthorized_permission_request',
                        'description': f'在用户拒绝隐私政策后仍然请求{", ".join(permission_detection["permission_types"])}权限',
                        'severity': 'high',
                        'evidence': {
                            'permission_types': permission_detection['permission_types'],
                            'request_time': time.time(),
                            'user_consent_status': 'refused'
                        },
                        'recommendation': '应在用户同意隐私政策后再请求相关权限'
                    })

            time.sleep(3)

        return violations

    def _monitor_reasonable_permission_requests(self):
        """监控合理的权限请求"""
        logger.info("监控权限请求的合理性")

        # 这里可以检测权限请求是否与功能相关
        # 例如：拍照功能请求相机权限是合理的，但壁纸APP请求通讯录权限就不合理
        pass

    def _start_continuous_monitoring(self):
        """启动持续监控"""
        self._update_progress("持续监控", 70, "启动持续监控模式...")

        # 如果还没有启动权限监控，则启动
        if not self.monitor_thread or not self.monitor_thread.is_alive():
            self._start_permission_monitoring()

        # 可以在这里添加其他持续监控任务
        logger.info("持续监控已启动，将持续检测违规行为")

    def _add_violation(self, category: str, violation: Dict[str, Any]):
        """添加违规记录"""
        violation['timestamp'] = time.time()
        violation['category'] = category

        self.violations.append(violation)

        if self.on_violation_found:
            self.on_violation_found(violation)

        logger.warning(f"发现{category}违规: {violation.get('type', 'unknown')} - {violation.get('description', '')}")

    def _update_progress(self, stage: str, progress: int, message: str):
        """更新进度"""
        if self.on_progress_update:
            self.on_progress_update({
                'stage': stage,
                'progress': progress,
                'message': message
            })

        logger.info(f"[{stage}] {progress}% - {message}")

    def get_detection_results(self) -> Dict[str, Any]:
        """获取检测结果"""
        # 按类别分组违规
        results = {
            'privacy_policy_violations': [],
            'permission_violations': [],
            'sdk_violations': [],
            'user_rights_violations': []
        }

        for violation in self.violations:
            category = violation.get('category', 'unknown')
            if f'{category}_violations' in results:
                results[f'{category}_violations'].append(violation)

        return results

    def stop_detection(self):
        """停止检测"""
        self.detection_active = False
        self.stop_monitoring = True

        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)

        logger.info("交互式检测已停止")
        
        # 3. 根据用户选择进行不同检测
        if self.user_consent_given is True:
            self._detect_post_consent_violations()
        elif self.user_consent_given is False:
            self._detect_pre_consent_violations()
        else:
            self._detect_no_consent_violations()
    
    def _monitor_app_startup(self):
        """监控APP启动过程"""
        logger.info("监控APP启动过程...")
        
        start_time = time.time()
        privacy_dialog_detected = False
        
        # 监控前10秒的UI变化
        while time.time() - start_time < 10:
            current_ui = self._get_current_ui_text()
            
            if current_ui != self.current_ui_text:
                self.current_ui_text = current_ui
                self._analyze_ui_content(current_ui)
                
                # 检测隐私政策弹窗
                if self._is_privacy_policy_dialog(current_ui):
                    privacy_dialog_detected = True
                    self.privacy_policy_shown = True
                    self.privacy_policy_content = current_ui
                    logger.info("检测到隐私政策弹窗")
                    break
            
            time.sleep(0.5)
        
        # 检测项目1: 无隐私政策
        if not privacy_dialog_detected:
            # 进一步检查是否有隐私政策入口
            policy_accessible = self._check_privacy_policy_accessibility()
            if not policy_accessible:
                self._add_violation(
                    "missing_privacy_policy",
                    "APP中未发现隐私政策",
                    {"startup_checked": True, "ui_content_checked": True},
                    "high", 1
                )
        
        # 检测项目2: 首次运行未提示隐私政策
        if not privacy_dialog_detected:
            self._add_violation(
                "no_first_run_prompt",
                "APP首次运行时未通过弹窗等明显方式提示用户阅读隐私政策",
                {"startup_time": time.time() - start_time, "dialog_detected": False},
                "high", 2
            )
    
    def _is_privacy_policy_dialog(self, ui_text: str) -> bool:
        """判断是否为隐私政策对话框"""
        privacy_keywords = [
            "隐私保护", "隐私政策", "隐私协议", "隐私条款",
            "用户协议", "服务协议", "使用条款",
            "个人信息", "数据收集", "信息收集",
            "同意", "不同意", "接受", "拒绝"
        ]
        
        # 检查是否包含隐私相关关键词
        privacy_count = sum(1 for keyword in privacy_keywords if keyword in ui_text)
        
        # 检查是否有同意/不同意按钮
        has_consent_buttons = ("同意" in ui_text and "不同意" in ui_text) or \
                             ("接受" in ui_text and "拒绝" in ui_text)
        
        # 检查文本长度（隐私政策通常较长）
        is_substantial_text = len(ui_text) > 100
        
        return privacy_count >= 3 and has_consent_buttons and is_substantial_text
    
    def _analyze_ui_content(self, ui_text: str):
        """分析UI内容"""
        # 记录UI事件
        ui_event = {
            "timestamp": time.time(),
            "content": ui_text,
            "content_length": len(ui_text),
            "type": "ui_change"
        }
        self.ui_events.append(ui_event)
        
        # 检测隐私政策内容质量
        if self.privacy_policy_shown and len(ui_text) > 200:
            self._analyze_privacy_policy_quality(ui_text)
    
    def _analyze_privacy_policy_quality(self, policy_text: str):
        """分析隐私政策质量"""
        issues = []
        
        # 检测项目5: 未逐项说明收集信息
        vague_terms = ["等", "例如", "包括但不限于", "等等", "其他"]
        found_vague = [term for term in vague_terms if term in policy_text]
        
        if found_vague:
            issues.append(f"使用模糊表述: {', '.join(found_vague)}")
        
        # 检查是否明确列出收集的信息类型
        required_info_types = [
            "设备信息", "位置信息", "联系人", "通话记录", 
            "短信", "相册", "麦克风", "摄像头"
        ]
        
        missing_types = [info_type for info_type in required_info_types 
                        if info_type not in policy_text]
        
        if len(missing_types) > len(required_info_types) * 0.5:
            issues.append(f"未明确说明收集的信息类型")
        
        # 检测项目8: 规则内容晦涩难懂
        technical_terms = [
            "API", "SDK", "Cookie", "Token", "加密", "哈希",
            "算法", "协议", "接口", "数据库", "服务器"
        ]
        
        tech_term_count = sum(1 for term in technical_terms if term in policy_text)
        
        # 检查句子长度
        sentences = re.split(r'[。！？]', policy_text)
        long_sentences = [s for s in sentences if len(s) > 100]
        
        complexity_score = tech_term_count * 2 + len(long_sentences) * 3
        
        if complexity_score > 20:
            issues.append(f"内容过于复杂，技术术语过多")
        
        if issues:
            if found_vague:
                self._add_violation(
                    "incomplete_information_list",
                    "未逐一列出APP及第三方SDK收集个人信息的目的、方式、范围",
                    {"vague_terms": found_vague, "missing_types": missing_types},
                    "high", 5
                )
            
            if complexity_score > 20:
                self._add_violation(
                    "complex_language",
                    "隐私政策使用大量专业术语、冗长表述，用户难以理解",
                    {"complexity_score": complexity_score, "tech_terms": tech_term_count},
                    "medium", 8
                )
    
    def _wait_for_user_consent(self):
        """等待用户同意/不同意选择"""
        if not self.privacy_policy_shown:
            return
        
        logger.info("等待用户选择同意/不同意...")
        
        # 提示测试人员进行选择
        if self.on_user_action_needed:
            self.on_user_action_needed("请在APP中选择'同意'或'不同意'隐私政策")
        
        start_time = time.time()
        
        # 监控用户点击
        while time.time() - start_time < 60:  # 最多等待60秒
            ui_text = self._get_current_ui_text()
            
            # 检测用户是否点击了同意/不同意
            if self._detect_user_consent_choice(ui_text):
                break
            
            time.sleep(1)
        
        if self.user_consent_given is None:
            logger.warning("未检测到用户选择，继续默认检测")
    
    def _detect_user_consent_choice(self, ui_text: str) -> bool:
        """检测用户同意选择"""
        # 如果隐私政策弹窗消失，说明用户做了选择
        if self.privacy_policy_shown and not self._is_privacy_policy_dialog(ui_text):
            # 尝试判断用户选择了什么
            # 这里可以通过UI变化、后续行为等判断
            
            # 简化判断：如果进入了主界面，认为是同意了
            if self._is_main_interface(ui_text):
                self.user_consent_given = True
                logger.info("检测到用户选择：同意")
            else:
                self.user_consent_given = False
                logger.info("检测到用户选择：不同意")
            
            return True
        
        return False
    
    def _detect_pre_consent_violations(self):
        """检测用户不同意后的违规行为"""
        logger.info("开始检测用户不同意后的违规行为...")
        
        # 检测项目10: 拒绝后继续收集或频繁骚扰
        start_time = time.time()
        
        while time.time() - start_time < 30:  # 监控30秒
            # 检测是否有数据收集行为
            data_collections = self._get_recent_data_collections()
            
            for collection in data_collections:
                if collection.get('after_denial', False):
                    self._add_violation(
                        "continued_collection_after_denial",
                        f"用户拒绝后仍收集{collection.get('type', '未知')}信息",
                        collection,
                        "high", 10
                    )
            
            # 检测是否频繁弹窗
            if self._detect_frequent_permission_requests():
                self._add_violation(
                    "frequent_permission_harassment",
                    "用户拒绝后频繁弹窗请求授权",
                    {"request_frequency": "high"},
                    "high", 10
                )
            
            time.sleep(2)
    
    def _detect_post_consent_violations(self):
        """检测用户同意后的违规行为"""
        logger.info("开始检测用户同意后的违规行为...")
        
        # 检测项目3: 隐私政策访问路径过深
        access_path_depth = self._test_privacy_policy_access_depth()
        if access_path_depth > 4:
            self._add_violation(
                "deep_access_path",
                f"访问隐私政策需要超过4次点击（实际需要{access_path_depth}次）",
                {"click_count": access_path_depth, "max_allowed": 4},
                "medium", 3
            )
        
        # 持续监控权限申请和数据收集
        self._continuous_monitoring()
    
    def _continuous_monitoring(self):
        """持续监控模式"""
        logger.info("进入持续监控模式，按Ctrl+C停止...")
        
        try:
            while self.detection_active:
                # 监控权限申请
                self._monitor_permission_requests()
                
                # 监控数据收集
                self._monitor_data_collections()
                
                # 监控第三方SDK行为
                self._monitor_sdk_activities()
                
                time.sleep(2)
                
        except KeyboardInterrupt:
            logger.info("用户停止检测")
            self.detection_active = False
    
    def _get_current_ui_text(self) -> str:
        """获取当前UI文本"""
        try:
            # 这里需要实现UI文本提取
            # 可以使用uiautomator2或者通过Frida Hook UI相关方法
            return ""  # 简化实现
        except:
            return ""
    
    def _check_privacy_policy_accessibility(self) -> bool:
        """检查隐私政策可访问性"""
        # 简化实现
        return False
    
    def _is_main_interface(self, ui_text: str) -> bool:
        """判断是否为主界面"""
        main_interface_keywords = ["首页", "主页", "菜单", "设置", "我的"]
        return any(keyword in ui_text for keyword in main_interface_keywords)
    
    def _get_recent_data_collections(self) -> List[Dict]:
        """获取最近的数据收集记录"""
        return []  # 需要从Frida脚本获取
    
    def _detect_frequent_permission_requests(self) -> bool:
        """检测频繁权限请求"""
        return False  # 简化实现
    
    def _test_privacy_policy_access_depth(self) -> int:
        """测试隐私政策访问深度"""
        return 2  # 简化实现，实际需要UI自动化测试
    
    def _monitor_permission_requests(self):
        """监控权限申请"""
        pass  # 从Frida脚本获取数据
    
    def _monitor_data_collections(self):
        """监控数据收集"""
        pass  # 从Frida脚本获取数据
    
    def _monitor_sdk_activities(self):
        """监控SDK活动"""
        pass  # 从Frida脚本获取数据
    
    def _add_violation(self, violation_type: str, description: str, 
                      evidence: Dict[str, Any], severity: str, compliance_item: int):
        """添加违规记录"""
        violation = {
            "id": len(self.violations) + 1,
            "type": violation_type,
            "description": description,
            "evidence": evidence,
            "severity": severity,
            "compliance_item": compliance_item,
            "timestamp": time.time(),
            "category": "interactive"
        }
        
        self.violations.append(violation)
        logger.warning(f"发现违规: {description}")
        
        if self.on_violation_found:
            self.on_violation_found(violation)
    
    def stop_detection(self):
        """停止检测"""
        self.detection_active = False
        logger.info("检测已停止")
    
    def get_violations(self) -> List[Dict[str, Any]]:
        """获取所有违规记录"""
        return self.violations
