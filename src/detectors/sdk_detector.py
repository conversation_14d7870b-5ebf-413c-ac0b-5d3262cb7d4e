#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三方SDK检测模块
检测项目24-25：第三方数据共享和SDK违规行为检测
"""

import time
import re
import json
from typing import List, Dict, Any, Set
from urllib.parse import urlparse
from ..utils.logger import get_logger

logger = get_logger(__name__)

class SDKDetector:
    """第三方SDK合规检测器"""
    
    def __init__(self, frida_session, device, config):
        self.session = frida_session
        self.device = device
        self.config = config
        self.violations = []
        self.sdk_activities = []
        self.third_party_transfers = []
        self.network_requests = []
        self.start_time = time.time()
        
        # 已知第三方SDK列表
        self.known_sdks = {
            'com.google.android.gms': {
                'name': 'Google Play Services',
                'type': 'analytics',
                'domains': ['google-analytics.com', 'googletagmanager.com', 'doubleclick.net']
            },
            'com.facebook.ads': {
                'name': 'Facebook Ads',
                'type': 'advertising',
                'domains': ['facebook.com', 'connect.facebook.net']
            },
            'com.tencent.mm': {
                'name': 'WeChat SDK',
                'type': 'social',
                'domains': ['weixin.qq.com', 'wx.qq.com']
            },
            'com.alibaba': {
                'name': 'Alibaba SDK',
                'type': 'ecommerce',
                'domains': ['alibaba.com', 'taobao.com']
            },
            'com.baidu': {
                'name': 'Baidu SDK',
                'type': 'analytics',
                'domains': ['baidu.com', 'bdstatic.com']
            },
            'com.umeng': {
                'name': 'Umeng Analytics',
                'type': 'analytics',
                'domains': ['umeng.com', 'umengcloud.com']
            },
            'com.sina.weibo': {
                'name': 'Weibo SDK',
                'type': 'social',
                'domains': ['weibo.com', 'sina.com.cn']
            }
        }
        
        # 敏感数据类型
        self.sensitive_data_types = {
            'device_id', 'imei', 'mac_address', 'android_id',
            'location', 'contacts', 'phone_number', 'sms',
            'call_log', 'installed_apps', 'clipboard'
        }
    
    def detect_all(self) -> List[Dict[str, Any]]:
        """执行所有第三方SDK相关检测"""
        logger.info("开始第三方SDK合规检测")
        
        # 检测项目24: 未经同意向第三方提供信息
        self._detect_unauthorized_third_party_sharing()
        
        # 检测项目25: 第三方应用共享未授权
        self._detect_third_party_app_sharing()
        
        logger.info(f"第三方SDK检测完成，发现 {len(self.violations)} 个违规项")
        return self.violations
    
    def _detect_unauthorized_third_party_sharing(self):
        """检测项目24: 未经同意向第三方提供信息"""
        try:
            unauthorized_transfers = []
            
            # 分析网络请求，识别向第三方的数据传输
            for request in self.network_requests:
                url = request.get('url', '')
                domain = self._extract_domain(url)
                
                # 检查是否为第三方域名
                third_party_sdk = self._identify_third_party_by_domain(domain)
                if third_party_sdk:
                    # 检查传输的数据是否包含个人信息
                    data_content = request.get('data', '')
                    sensitive_data = self._extract_sensitive_data(data_content)
                    
                    if sensitive_data:
                        # 检查是否经过用户同意
                        user_consented = self._check_user_consent_for_sdk(third_party_sdk)
                        
                        if not user_consented:
                            unauthorized_transfers.append({
                                'sdk': third_party_sdk,
                                'domain': domain,
                                'url': url,
                                'sensitive_data': sensitive_data,
                                'timestamp': request.get('timestamp'),
                                'anonymized': self._is_data_anonymized(data_content)
                            })
            
            # 分析SDK活动中的数据传输
            for activity in self.sdk_activities:
                if activity.get('type') == 'sdk_network_request':
                    sdk_name = activity.get('sdk')
                    if sdk_name in self.known_sdks:
                        # 检查是否传输敏感数据
                        if self._contains_sensitive_data(activity):
                            user_consented = self._check_user_consent_for_sdk(sdk_name)
                            
                            if not user_consented:
                                unauthorized_transfers.append({
                                    'sdk': sdk_name,
                                    'activity': activity,
                                    'reason': 'SDK传输敏感数据未经用户同意'
                                })
            
            if unauthorized_transfers:
                self._add_violation(
                    violation_type="unauthorized_third_party_sharing",
                    description="未匿名化且未经用户同意，通过SDK等向第三方传输个人信息",
                    evidence={
                        "unauthorized_transfers": unauthorized_transfers,
                        "count": len(unauthorized_transfers),
                        "affected_sdks": list(set([t.get('sdk') for t in unauthorized_transfers]))
                    },
                    severity="high",
                    compliance_item=24
                )
                
        except Exception as e:
            logger.error(f"检测未授权第三方共享时发生错误: {e}")
    
    def _detect_third_party_app_sharing(self):
        """检测项目25: 第三方应用共享未授权"""
        try:
            unauthorized_app_sharing = []
            
            # 检查APP跳转到第三方应用时的数据共享
            app_jumps = self._detect_third_party_app_jumps()
            
            for jump in app_jumps:
                target_app = jump.get('target_package')
                shared_data = jump.get('shared_data', {})
                
                # 检查共享的数据是否包含个人信息
                if self._contains_personal_info(shared_data):
                    # 检查是否经过用户同意
                    user_consented = self._check_user_consent_for_app_sharing(target_app)
                    
                    if not user_consented:
                        unauthorized_app_sharing.append({
                            'target_app': target_app,
                            'shared_data': shared_data,
                            'timestamp': jump.get('timestamp'),
                            'data_types': self._identify_data_types(shared_data)
                        })
            
            # 检查Intent传递的敏感数据
            intent_sharing = self._detect_intent_data_sharing()
            for intent in intent_sharing:
                if intent.get('contains_personal_info') and not intent.get('user_consented'):
                    unauthorized_app_sharing.append(intent)
            
            if unauthorized_app_sharing:
                self._add_violation(
                    violation_type="third_party_app_sharing",
                    description="APP接入第三方应用时未经用户同意共享个人信息",
                    evidence={
                        "unauthorized_sharing": unauthorized_app_sharing,
                        "count": len(unauthorized_app_sharing)
                    },
                    severity="high",
                    compliance_item=25
                )
                
        except Exception as e:
            logger.error(f"检测第三方应用共享时发生错误: {e}")
    
    def _extract_domain(self, url: str) -> str:
        """从URL提取域名"""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except:
            return ""
    
    def _identify_third_party_by_domain(self, domain: str) -> str:
        """通过域名识别第三方SDK"""
        for sdk_package, sdk_info in self.known_sdks.items():
            for sdk_domain in sdk_info.get('domains', []):
                if sdk_domain in domain:
                    return sdk_package
        return None
    
    def _extract_sensitive_data(self, data_content: str) -> List[str]:
        """从数据内容中提取敏感信息"""
        sensitive_data = []
        
        # 检查IMEI
        imei_pattern = r'\b\d{15}\b'
        if re.search(imei_pattern, data_content):
            sensitive_data.append('IMEI')
        
        # 检查手机号
        phone_pattern = r'\b1[3-9]\d{9}\b'
        if re.search(phone_pattern, data_content):
            sensitive_data.append('手机号')
        
        # 检查MAC地址
        mac_pattern = r'\b([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})\b'
        if re.search(mac_pattern, data_content):
            sensitive_data.append('MAC地址')
        
        # 检查坐标信息
        coord_pattern = r'\b\d+\.\d+,\d+\.\d+\b'
        if re.search(coord_pattern, data_content):
            sensitive_data.append('位置坐标')
        
        return sensitive_data
    
    def _is_data_anonymized(self, data_content: str) -> bool:
        """检查数据是否已匿名化"""
        # 简化实现：检查是否包含明显的个人标识符
        identifiers = ['imei', 'phone', 'email', 'name', 'id']
        content_lower = data_content.lower()
        
        for identifier in identifiers:
            if identifier in content_lower:
                return False
        
        return True
    
    def _check_user_consent_for_sdk(self, sdk_name: str) -> bool:
        """检查用户是否同意SDK数据收集"""
        # 这里需要与用户同意记录进行比对
        # 简化实现
        return False
    
    def _contains_sensitive_data(self, activity: Dict[str, Any]) -> bool:
        """检查活动是否包含敏感数据"""
        activity_str = json.dumps(activity).lower()
        
        for data_type in self.sensitive_data_types:
            if data_type in activity_str:
                return True
        
        return False
    
    def _detect_third_party_app_jumps(self) -> List[Dict[str, Any]]:
        """检测第三方应用跳转"""
        # 这需要监控Intent和Activity启动
        # 简化实现
        return []
    
    def _contains_personal_info(self, shared_data: Dict[str, Any]) -> bool:
        """检查共享数据是否包含个人信息"""
        data_str = json.dumps(shared_data).lower()
        
        personal_info_indicators = [
            'phone', 'email', 'name', 'id', 'location',
            'contact', 'address', 'birthday'
        ]
        
        for indicator in personal_info_indicators:
            if indicator in data_str:
                return True
        
        return False
    
    def _check_user_consent_for_app_sharing(self, target_app: str) -> bool:
        """检查用户是否同意应用间数据共享"""
        # 简化实现
        return False
    
    def _identify_data_types(self, shared_data: Dict[str, Any]) -> List[str]:
        """识别共享数据的类型"""
        data_types = []
        data_str = json.dumps(shared_data).lower()
        
        type_mapping = {
            'phone': '手机号',
            'email': '邮箱',
            'name': '姓名',
            'location': '位置',
            'contact': '联系人'
        }
        
        for key, value in type_mapping.items():
            if key in data_str:
                data_types.append(value)
        
        return data_types
    
    def _detect_intent_data_sharing(self) -> List[Dict[str, Any]]:
        """检测Intent数据共享"""
        # 这需要Hook Intent相关方法
        # 简化实现
        return []
    
    def _add_violation(self, violation_type: str, description: str, 
                      evidence: Dict[str, Any], severity: str, compliance_item: int):
        """添加违规记录"""
        violation = {
            "id": len(self.violations) + 1,
            "type": violation_type,
            "description": description,
            "evidence": evidence,
            "severity": severity,
            "compliance_item": compliance_item,
            "timestamp": time.time(),
            "category": "third_party_sdk"
        }
        
        self.violations.append(violation)
        logger.warning(f"发现第三方SDK违规: {description}")
    
    def update_network_requests(self, requests: List[Dict[str, Any]]):
        """更新网络请求记录"""
        self.network_requests.extend(requests)
    
    def update_sdk_activities(self, activities: List[Dict[str, Any]]):
        """更新SDK活动记录"""
        self.sdk_activities.extend(activities)
