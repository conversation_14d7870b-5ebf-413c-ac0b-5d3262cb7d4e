#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限和数据收集检测模块
检测项目9-23：权限申请、数据收集行为的合规性检测
"""

import time
import json
from typing import List, Dict, Any, Set
from ..utils.logger import get_logger

logger = get_logger(__name__)

class PermissionDetector:
    """权限和数据收集合规检测器"""
    
    def __init__(self, frida_session, device, config):
        self.session = frida_session
        self.device = device
        self.config = config
        self.violations = []
        self.permission_requests = []
        self.data_collections = []
        self.user_consented = False
        self.user_denied_permissions = set()
        self.start_time = time.time()
        
        # 敏感权限列表
        self.sensitive_permissions = {
            'android.permission.ACCESS_FINE_LOCATION': '精确位置',
            'android.permission.ACCESS_COARSE_LOCATION': '大致位置',
            'android.permission.READ_CONTACTS': '联系人',
            'android.permission.READ_PHONE_STATE': '电话状态',
            'android.permission.READ_SMS': '短信',
            'android.permission.CAMERA': '摄像头',
            'android.permission.RECORD_AUDIO': '麦克风',
            'android.permission.READ_EXTERNAL_STORAGE': '存储',
            'android.permission.WRITE_EXTERNAL_STORAGE': '存储写入',
            'android.permission.READ_CALL_LOG': '通话记录'
        }
        
        # 业务功能与权限的合理映射
        self.reasonable_permissions = {
            'camera_app': ['android.permission.CAMERA', 'android.permission.WRITE_EXTERNAL_STORAGE'],
            'map_app': ['android.permission.ACCESS_FINE_LOCATION', 'android.permission.ACCESS_COARSE_LOCATION'],
            'social_app': ['android.permission.READ_CONTACTS', 'android.permission.CAMERA'],
            'music_app': ['android.permission.READ_EXTERNAL_STORAGE', 'android.permission.RECORD_AUDIO'],
            'weather_app': ['android.permission.ACCESS_COARSE_LOCATION']
        }
    
    def detect_all(self) -> List[Dict[str, Any]]:
        """执行所有权限和数据收集相关检测"""
        logger.info("开始权限和数据收集合规检测")
        
        # 检测项目9: 提前收集信息或权限
        self._detect_premature_collection()
        
        # 检测项目10: 拒绝后继续收集或频繁骚扰
        self._detect_continued_collection_after_denial()
        
        # 检测项目11: 超范围收集信息或权限
        self._detect_over_scope_collection()
        
        # 检测项目12: 非明示同意（默认勾选）
        self._detect_non_explicit_consent()
        
        # 检测项目13: 擅自恢复默认权限
        self._detect_unauthorized_permission_reset()
        
        # 检测项目14: 未提供非定向推送选项
        self._detect_no_personalization_opt_out()
        
        # 检测项目15: 欺诈诱骗同意
        self._detect_deceptive_consent()
        
        # 检测项目16: 未提供撤回同意渠道
        self._detect_no_consent_withdrawal()
        
        # 检测项目17: 违反声明的收集规则
        self._detect_collection_rule_violation()
        
        # 检测项目18: 收集与业务无关的信息
        self._detect_irrelevant_data_collection()
        
        # 检测项目19: 拒绝非必要权限即禁用功能
        self._detect_function_blocking_on_denial()
        
        # 检测项目20: 新增功能超原有同意范围
        self._detect_new_function_over_scope()
        
        # 检测项目21: 超频次收集信息
        self._detect_excessive_collection_frequency()
        
        # 检测项目22: 以"改善体验"强制收集
        self._detect_forced_collection_for_experience()
        
        # 检测项目23: 一次性索要多个权限
        self._detect_bulk_permission_request()
        
        logger.info(f"权限和数据收集检测完成，发现 {len(self.violations)} 个违规项")
        return self.violations
    
    def _detect_premature_collection(self):
        """检测项目9: 提前收集信息或权限"""
        try:
            # 检查在用户同意隐私政策前是否有数据收集行为
            premature_collections = []
            
            for collection in self.data_collections:
                if collection.get('beforeConsent', False):
                    premature_collections.append(collection)
            
            if premature_collections:
                self._add_violation(
                    violation_type="premature_collection",
                    description="在用户同意隐私政策前收集个人信息或申请权限",
                    evidence={
                        "premature_collections": premature_collections,
                        "count": len(premature_collections)
                    },
                    severity="high",
                    compliance_item=9
                )
                
        except Exception as e:
            logger.error(f"检测提前收集时发生错误: {e}")
    
    def _detect_continued_collection_after_denial(self):
        """检测项目10: 拒绝后继续收集或频繁骚扰"""
        try:
            # 检查用户拒绝后是否继续收集
            continued_collections = []
            frequent_requests = {}
            
            for request in self.permission_requests:
                permission = request.get('permission')
                timestamp = request.get('timestamp', 0)
                
                if permission in self.user_denied_permissions:
                    # 检查拒绝后是否继续申请
                    if timestamp > self._get_denial_time(permission):
                        if permission not in frequent_requests:
                            frequent_requests[permission] = []
                        frequent_requests[permission].append(timestamp)
            
            # 检查频繁申请（1小时内超过3次）
            for permission, timestamps in frequent_requests.items():
                if len(timestamps) > 3:
                    time_span = max(timestamps) - min(timestamps)
                    if time_span < 3600:  # 1小时
                        self._add_violation(
                            violation_type="frequent_permission_harassment",
                            description=f"用户拒绝{self.sensitive_permissions.get(permission, permission)}权限后频繁弹窗请求",
                            evidence={
                                "permission": permission,
                                "request_count": len(timestamps),
                                "time_span_seconds": time_span,
                                "timestamps": timestamps
                            },
                            severity="high",
                            compliance_item=10
                        )
                        
        except Exception as e:
            logger.error(f"检测拒绝后继续收集时发生错误: {e}")
    
    def _detect_over_scope_collection(self):
        """检测项目11: 超范围收集信息或权限"""
        try:
            # 检查实际收集的信息是否超出用户授权范围
            authorized_permissions = self._get_authorized_permissions()
            
            for collection in self.data_collections:
                collection_type = collection.get('type')
                required_permission = self._get_required_permission(collection_type)
                
                if required_permission and required_permission not in authorized_permissions:
                    self._add_violation(
                        violation_type="over_scope_collection",
                        description=f"收集{collection_type}信息超出用户授权范围",
                        evidence={
                            "collection_type": collection_type,
                            "required_permission": required_permission,
                            "authorized_permissions": list(authorized_permissions),
                            "collection_details": collection
                        },
                        severity="high",
                        compliance_item=11
                    )
                    
        except Exception as e:
            logger.error(f"检测超范围收集时发生错误: {e}")
    
    def _detect_non_explicit_consent(self):
        """检测项目12: 非明示同意（默认勾选）"""
        try:
            # 检查是否存在默认勾选的同意选项
            # 这需要UI分析来检测
            default_checked_items = self._find_default_checked_items()
            
            if default_checked_items:
                self._add_violation(
                    violation_type="non_explicit_consent",
                    description="以默认勾选、登录即同意等方式误导用户非主动同意",
                    evidence={
                        "default_checked_items": default_checked_items
                    },
                    severity="high",
                    compliance_item=12
                )
                
        except Exception as e:
            logger.error(f"检测非明示同意时发生错误: {e}")
    
    def _detect_unauthorized_permission_reset(self):
        """检测项目13: 擅自恢复默认权限"""
        try:
            # 检查APP更新或重启后是否未经同意恢复权限
            # 这需要跨会话的状态比较
            reset_permissions = self._check_permission_reset()
            
            if reset_permissions:
                self._add_violation(
                    violation_type="unauthorized_permission_reset",
                    description="APP更新或重启后未经用户同意恢复已关闭的权限",
                    evidence={
                        "reset_permissions": reset_permissions
                    },
                    severity="medium",
                    compliance_item=13
                )
                
        except Exception as e:
            logger.error(f"检测权限重置时发生错误: {e}")
    
    def _detect_no_personalization_opt_out(self):
        """检测项目14: 未提供非定向推送选项"""
        try:
            # 检查是否提供关闭个性化推荐的选项
            has_opt_out = self._check_personalization_opt_out()
            
            if not has_opt_out:
                self._add_violation(
                    violation_type="no_personalization_opt_out",
                    description="利用算法定向推送时未提供关闭个性化推荐的选项",
                    evidence={
                        "opt_out_option_found": False
                    },
                    severity="medium",
                    compliance_item=14
                )
                
        except Exception as e:
            logger.error(f"检测个性化推送选项时发生错误: {e}")
    
    def _detect_deceptive_consent(self):
        """检测项目15: 欺诈诱骗同意"""
        try:
            # 检查是否存在欺骗性的同意提示
            deceptive_messages = self._find_deceptive_consent_messages()
            
            if deceptive_messages:
                self._add_violation(
                    violation_type="deceptive_consent",
                    description="通过欺骗、隐瞒真实目的等方式误导用户授权",
                    evidence={
                        "deceptive_messages": deceptive_messages
                    },
                    severity="high",
                    compliance_item=15
                )
                
        except Exception as e:
            logger.error(f"检测欺诈诱骗时发生错误: {e}")
    
    def _detect_no_consent_withdrawal(self):
        """检测项目16: 未提供撤回同意渠道"""
        try:
            # 检查是否提供便捷的撤回同意路径
            withdrawal_path = self._find_consent_withdrawal_path()
            
            if not withdrawal_path:
                self._add_violation(
                    violation_type="no_consent_withdrawal",
                    description="未在APP中提供便捷的撤回同意路径",
                    evidence={
                        "withdrawal_path_found": False
                    },
                    severity="medium",
                    compliance_item=16
                )
                
        except Exception as e:
            logger.error(f"检测撤回同意渠道时发生错误: {e}")
    
    def _detect_collection_rule_violation(self):
        """检测项目17: 违反声明的收集规则"""
        try:
            # 比较实际收集行为与隐私政策声明
            policy_rules = self._get_privacy_policy_rules()
            actual_collections = self.data_collections
            
            violations = []
            for collection in actual_collections:
                if not self._is_collection_allowed_by_policy(collection, policy_rules):
                    violations.append(collection)
            
            if violations:
                self._add_violation(
                    violation_type="collection_rule_violation",
                    description="实际收集行为与隐私政策声明不一致",
                    evidence={
                        "policy_violations": violations,
                        "count": len(violations)
                    },
                    severity="high",
                    compliance_item=17
                )
                
        except Exception as e:
            logger.error(f"检测收集规则违反时发生错误: {e}")
    
    def _detect_irrelevant_data_collection(self):
        """检测项目18: 收集与业务无关的信息"""
        try:
            # 分析收集的信息是否与APP功能相关
            app_type = self._identify_app_type()
            reasonable_perms = self.reasonable_permissions.get(app_type, [])
            
            irrelevant_collections = []
            for collection in self.data_collections:
                required_perm = self._get_required_permission(collection.get('type'))
                if required_perm and required_perm not in reasonable_perms:
                    irrelevant_collections.append(collection)
            
            if irrelevant_collections:
                self._add_violation(
                    violation_type="irrelevant_data_collection",
                    description="收集的信息与现有功能无关",
                    evidence={
                        "app_type": app_type,
                        "reasonable_permissions": reasonable_perms,
                        "irrelevant_collections": irrelevant_collections
                    },
                    severity="medium",
                    compliance_item=18
                )
                
        except Exception as e:
            logger.error(f"检测无关数据收集时发生错误: {e}")
    
    def _add_violation(self, violation_type: str, description: str, 
                      evidence: Dict[str, Any], severity: str, compliance_item: int):
        """添加违规记录"""
        violation = {
            "id": len(self.violations) + 1,
            "type": violation_type,
            "description": description,
            "evidence": evidence,
            "severity": severity,
            "compliance_item": compliance_item,
            "timestamp": time.time(),
            "category": "permission_data"
        }
        
        self.violations.append(violation)
        logger.warning(f"发现权限数据违规: {description}")
    
    # 辅助方法（简化实现）
    def _get_denial_time(self, permission: str) -> float:
        """获取权限拒绝时间"""
        return time.time() - 3600  # 简化实现
    
    def _get_authorized_permissions(self) -> Set[str]:
        """获取用户授权的权限"""
        return set()  # 简化实现
    
    def _get_required_permission(self, collection_type: str) -> str:
        """根据收集类型获取所需权限"""
        mapping = {
            'location': 'android.permission.ACCESS_FINE_LOCATION',
            'contacts': 'android.permission.READ_CONTACTS',
            'phone_number': 'android.permission.READ_PHONE_STATE',
            'device_id': 'android.permission.READ_PHONE_STATE'
        }
        return mapping.get(collection_type)
    
    def _find_default_checked_items(self) -> List[str]:
        """查找默认勾选项"""
        return []  # 需要UI分析实现
    
    def _check_permission_reset(self) -> List[str]:
        """检查权限重置"""
        return []  # 需要状态持久化实现
    
    def _check_personalization_opt_out(self) -> bool:
        """检查个性化推送退出选项"""
        return False  # 需要UI分析实现
    
    def _find_deceptive_consent_messages(self) -> List[str]:
        """查找欺骗性同意消息"""
        return []  # 需要文本分析实现
    
    def _find_consent_withdrawal_path(self) -> bool:
        """查找撤回同意路径"""
        return False  # 需要UI分析实现
    
    def _get_privacy_policy_rules(self) -> Dict[str, Any]:
        """获取隐私政策规则"""
        return {}  # 需要政策解析实现
    
    def _is_collection_allowed_by_policy(self, collection: Dict[str, Any], 
                                       policy_rules: Dict[str, Any]) -> bool:
        """检查收集是否符合政策"""
        return True  # 简化实现
    
    def _detect_function_blocking_on_denial(self):
        """检测项目19: 拒绝非必要权限即禁用功能"""
        try:
            # 检查用户拒绝非必要权限后是否禁用核心功能
            blocked_functions = []

            for permission in self.user_denied_permissions:
                if self._is_non_essential_permission(permission):
                    blocked_functions_for_perm = self._check_function_blocking(permission)
                    if blocked_functions_for_perm:
                        blocked_functions.extend(blocked_functions_for_perm)

            if blocked_functions:
                self._add_violation(
                    violation_type="function_blocking_on_denial",
                    description="用户拒绝非必要权限后禁用核心功能",
                    evidence={
                        "blocked_functions": blocked_functions
                    },
                    severity="high",
                    compliance_item=19
                )

        except Exception as e:
            logger.error(f"检测功能禁用时发生错误: {e}")

    def _detect_new_function_over_scope(self):
        """检测项目20: 新增功能超原有同意范围"""
        try:
            # 检查新增功能是否超出原有同意范围
            new_functions = self._detect_new_functions()
            over_scope_functions = []

            for func in new_functions:
                required_permissions = func.get('required_permissions', [])
                if not self._are_permissions_in_original_scope(required_permissions):
                    over_scope_functions.append(func)

            if over_scope_functions:
                self._add_violation(
                    violation_type="new_function_over_scope",
                    description="新增业务功能索要超出用户原同意范围的信息",
                    evidence={
                        "over_scope_functions": over_scope_functions
                    },
                    severity="medium",
                    compliance_item=20
                )

        except Exception as e:
            logger.error(f"检测新功能超范围时发生错误: {e}")

    def _detect_excessive_collection_frequency(self):
        """检测项目21: 超频次收集信息"""
        try:
            # 分析数据收集频率
            collection_frequency = {}

            for collection in self.data_collections:
                collection_type = collection.get('type')
                timestamp = collection.get('timestamp', 0)

                if collection_type not in collection_frequency:
                    collection_frequency[collection_type] = []
                collection_frequency[collection_type].append(timestamp)

            excessive_collections = []
            for collection_type, timestamps in collection_frequency.items():
                if len(timestamps) > 1:
                    # 计算平均间隔
                    timestamps.sort()
                    intervals = []
                    for i in range(1, len(timestamps)):
                        intervals.append(timestamps[i] - timestamps[i-1])

                    avg_interval = sum(intervals) / len(intervals)
                    reasonable_interval = self._get_reasonable_interval(collection_type)

                    if avg_interval < reasonable_interval:
                        excessive_collections.append({
                            'type': collection_type,
                            'avg_interval': avg_interval,
                            'reasonable_interval': reasonable_interval,
                            'count': len(timestamps)
                        })

            if excessive_collections:
                self._add_violation(
                    violation_type="excessive_collection_frequency",
                    description="收集频率超出业务必需",
                    evidence={
                        "excessive_collections": excessive_collections
                    },
                    severity="medium",
                    compliance_item=21
                )

        except Exception as e:
            logger.error(f"检测收集频率时发生错误: {e}")

    def _detect_forced_collection_for_experience(self):
        """检测项目22: 以"改善体验"强制收集"""
        try:
            # 检查是否以模糊理由强制收集敏感信息
            forced_collections = []

            for collection in self.data_collections:
                if self._is_sensitive_data(collection.get('type')):
                    justification = collection.get('justification', '')
                    if self._is_vague_justification(justification):
                        forced_collections.append(collection)

            if forced_collections:
                self._add_violation(
                    violation_type="forced_collection_for_experience",
                    description="仅以优化服务、定向推送等模糊理由强制收集敏感信息",
                    evidence={
                        "forced_collections": forced_collections
                    },
                    severity="high",
                    compliance_item=22
                )

        except Exception as e:
            logger.error(f"检测强制收集时发生错误: {e}")

    def _detect_bulk_permission_request(self):
        """检测项目23: 一次性索要多个权限"""
        try:
            # 检查是否一次性申请多个权限
            bulk_requests = []

            for request in self.permission_requests:
                permissions = request.get('permissions', [])
                if len(permissions) > 3:  # 超过3个权限视为批量申请
                    bulk_requests.append(request)

            if bulk_requests:
                self._add_violation(
                    violation_type="bulk_permission_request",
                    description="要求用户一次性同意打开多个权限",
                    evidence={
                        "bulk_requests": bulk_requests,
                        "count": len(bulk_requests)
                    },
                    severity="medium",
                    compliance_item=23
                )

        except Exception as e:
            logger.error(f"检测批量权限申请时发生错误: {e}")

    # 新增辅助方法
    def _is_non_essential_permission(self, permission: str) -> bool:
        """判断是否为非必要权限"""
        essential_permissions = [
            'android.permission.INTERNET',
            'android.permission.ACCESS_NETWORK_STATE'
        ]
        return permission not in essential_permissions

    def _check_function_blocking(self, permission: str) -> List[str]:
        """检查权限拒绝后被禁用的功能"""
        return []  # 需要功能测试实现

    def _detect_new_functions(self) -> List[Dict[str, Any]]:
        """检测新增功能"""
        return []  # 需要版本比较实现

    def _are_permissions_in_original_scope(self, permissions: List[str]) -> bool:
        """检查权限是否在原有范围内"""
        return True  # 需要历史记录比较

    def _get_reasonable_interval(self, collection_type: str) -> float:
        """获取合理的收集间隔（秒）"""
        intervals = {
            'location': 300,  # 5分钟
            'device_id': 86400,  # 24小时
            'contacts': 3600,  # 1小时
            'phone_number': 86400  # 24小时
        }
        return intervals.get(collection_type, 3600)

    def _is_sensitive_data(self, data_type: str) -> bool:
        """判断是否为敏感数据"""
        sensitive_types = [
            'device_id', 'location', 'contacts', 'phone_number',
            'imei', 'mac_address', 'call_log', 'sms'
        ]
        return data_type in sensitive_types

    def _is_vague_justification(self, justification: str) -> bool:
        """判断是否为模糊理由"""
        vague_terms = [
            '优化服务', '改善体验', '定向推送', '个性化推荐',
            '提升性能', '用户体验', '服务质量'
        ]
        return any(term in justification for term in vague_terms)

    def _identify_app_type(self) -> str:
        """识别APP类型"""
        return 'unknown'  # 需要APP分析实现
