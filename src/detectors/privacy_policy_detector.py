#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隐私政策检测模块
检测项目1-8：隐私政策相关的合规性检测
"""

import time
import re
from typing import List, Dict, Any
from ..utils.logger import get_logger

logger = get_logger(__name__)

class PrivacyPolicyDetector:
    """隐私政策合规检测器"""
    
    def __init__(self, frida_session, device):
        self.session = frida_session
        self.device = device
        self.violations = []
        self.ui_events = []
        self.privacy_policy_shown = False
        self.privacy_policy_accessible = False
        self.privacy_policy_content = ""
        self.start_time = time.time()
        
    def detect_all(self) -> List[Dict[str, Any]]:
        """执行所有隐私政策相关检测"""
        logger.info("开始隐私政策合规检测")
        
        # 检测项目1: 无隐私政策或规则缺失
        self._detect_missing_privacy_policy()
        
        # 检测项目2: 首次运行未提示隐私政策
        self._detect_no_first_run_prompt()
        
        # 检测项目3: 隐私政策访问路径过深
        self._detect_deep_access_path()
        
        # 检测项目4: 隐私政策难以阅读
        self._detect_readability_issues()
        
        # 检测项目5: 未逐项说明收集信息
        self._detect_incomplete_information_list()
        
        # 检测项目6: 变更规则未通知用户
        self._detect_no_change_notification()
        
        # 检测项目7: 敏感权限未同步告知目的
        self._detect_permission_purpose_missing()
        
        # 检测项目8: 规则内容晦涩难懂
        self._detect_complex_language()
        
        logger.info(f"隐私政策检测完成，发现 {len(self.violations)} 个违规项")
        return self.violations

    def _comprehensive_privacy_policy_search(self) -> Dict[str, Any]:
        """全面搜索隐私政策"""
        search_result = {
            'policy_found': False,
            'found_locations': [],
            'searched_locations': [],
            'search_methods': [],
            'search_completeness': 0.0,
            'locations_checked': 0
        }

        # 定义搜索位置和方法
        search_locations = [
            {'name': '启动弹窗', 'method': 'startup_popup', 'weight': 0.4},
            {'name': '设置菜单', 'method': 'settings_menu', 'weight': 0.2},
            {'name': '关于页面', 'method': 'about_page', 'weight': 0.15},
            {'name': '用户协议', 'method': 'user_agreement', 'weight': 0.15},
            {'name': '法律声明', 'method': 'legal_notice', 'weight': 0.1}
        ]

        total_weight = sum(loc['weight'] for loc in search_locations)
        completed_weight = 0.0

        for location in search_locations:
            try:
                search_result['searched_locations'].append(location['name'])
                search_result['search_methods'].append(location['method'])
                search_result['locations_checked'] += 1

                # 执行具体的搜索方法
                found = self._search_location(location['method'])

                if found:
                    search_result['policy_found'] = True
                    search_result['found_locations'].append(location['name'])

                completed_weight += location['weight']

            except Exception as e:
                logger.warning(f"搜索{location['name']}时发生错误: {e}")

        search_result['search_completeness'] = completed_weight / total_weight
        return search_result

    def _search_location(self, method: str) -> bool:
        """在指定位置搜索隐私政策"""
        if method == 'startup_popup':
            return self._search_startup_popup()
        elif method == 'settings_menu':
            return self._search_settings_menu()
        elif method == 'about_page':
            return self._search_about_page()
        elif method == 'user_agreement':
            return self._search_user_agreement()
        elif method == 'legal_notice':
            return self._search_legal_notice()

        return False

    def _search_startup_popup(self) -> bool:
        """搜索启动弹窗中的隐私政策"""
        try:
            from ..utils.ui_analyzer import UIAnalyzer

            ui_analyzer = UIAnalyzer()
            ui_state = ui_analyzer.get_current_ui_state()

            if ui_state:
                policy_detection = ui_analyzer.detect_privacy_policy_popup(ui_state)
                return policy_detection['detected'] and policy_detection['confidence'] > 70

            return False

        except Exception as e:
            logger.warning(f"搜索启动弹窗隐私政策时发生错误: {e}")
            return False

    def _search_settings_menu(self) -> bool:
        """搜索设置菜单中的隐私政策"""
        # 这里应该实现导航到设置菜单并搜索隐私政策的逻辑
        # 简化实现，返回False表示未找到
        return False

    def _search_about_page(self) -> bool:
        """搜索关于页面中的隐私政策"""
        # 这里应该实现导航到关于页面并搜索隐私政策的逻辑
        return False

    def _search_user_agreement(self) -> bool:
        """搜索用户协议中的隐私政策"""
        # 这里应该实现搜索用户协议的逻辑
        return False

    def _search_legal_notice(self) -> bool:
        """搜索法律声明中的隐私政策"""
        # 这里应该实现搜索法律声明的逻辑
        return False

    def _detect_startup_privacy_prompt(self, ui_analyzer) -> Dict[str, Any]:
        """检测启动时的隐私政策提示"""
        result = {
            'prompt_detected': False,
            'detection_confidence': 0,
            'prompt_type': None,
            'evidence': {}
        }

        try:
            # 等待APP启动
            time.sleep(3)

            ui_state = ui_analyzer.get_current_ui_state()
            if ui_state:
                policy_detection = ui_analyzer.detect_privacy_policy_popup(ui_state)

                result['prompt_detected'] = policy_detection['detected']
                result['detection_confidence'] = policy_detection['confidence']
                result['evidence'] = {
                    'privacy_matches': policy_detection.get('privacy_matches', 0),
                    'action_matches': policy_detection.get('action_matches', 0),
                    'has_consent_buttons': policy_detection.get('has_consent_buttons', False)
                }

                if policy_detection['detected']:
                    result['prompt_type'] = 'popup_dialog'
                    self.privacy_policy_shown = True

        except Exception as e:
            logger.error(f"检测启动隐私政策提示时发生错误: {e}")

        return result

    def _check_alternative_privacy_prompts(self) -> Dict[str, Any]:
        """检查其他形式的隐私政策提示"""
        result = {
            'found': False,
            'prompt_types': [],
            'locations': []
        }

        # 检查是否有其他形式的隐私政策提示
        # 例如：首页横幅、设置页面提示等

        return result
    
    def _detect_missing_privacy_policy(self):
        """检测项目1: 无隐私政策或规则缺失 - 改进版本，减少误报"""
        try:
            # 使用更智能的方式检查隐私政策
            policy_search_result = self._comprehensive_privacy_policy_search()

            # 只有在确实没有找到任何隐私政策相关内容时才报告违规
            if (not policy_search_result['policy_found'] and
                policy_search_result['search_completeness'] > 0.8):  # 搜索完整度超过80%

                self._add_violation(
                    violation_type="missing_privacy_policy",
                    description="APP中未发现隐私政策",
                    evidence={
                        "searched_locations": policy_search_result['searched_locations'],
                        "search_methods": policy_search_result['search_methods'],
                        "policy_found": False,
                        "search_completeness": policy_search_result['search_completeness'],
                        "potential_locations_checked": policy_search_result['locations_checked']
                    },
                    severity="high",
                    compliance_item=1
                )
            elif policy_search_result['policy_found']:
                logger.info(f"发现隐私政策: {policy_search_result['found_locations']}")
                self.privacy_policy_shown = True
                self.privacy_policy_accessible = True

        except Exception as e:
            logger.error(f"检测隐私政策缺失时发生错误: {e}")
    
    def _detect_no_first_run_prompt(self):
        """检测项目2: 首次运行未提示隐私政策 - 改进版本"""
        try:
            # 使用UI分析器检测启动时的隐私政策弹窗
            from ..utils.ui_analyzer import UIAnalyzer

            ui_analyzer = UIAnalyzer()
            startup_detection_result = self._detect_startup_privacy_prompt(ui_analyzer)

            # 只有在确实没有检测到隐私政策弹窗时才报告违规
            if not startup_detection_result['prompt_detected']:
                # 进一步验证：检查是否在其他地方有隐私政策提示
                alternative_prompts = self._check_alternative_privacy_prompts()

                if not alternative_prompts['found']:
                    self._add_violation(
                    violation_type="no_first_run_prompt",
                    description="APP首次运行时未通过弹窗等明显方式提示用户阅读隐私政策",
                    evidence={
                        "startup_time": time.time() - self.start_time,
                        "privacy_prompt_detected": False,
                        "ui_events_count": len(self.ui_events)
                    },
                    severity="high",
                    compliance_item=2
                )
                
        except Exception as e:
            logger.error(f"检测首次运行提示时发生错误: {e}")
    
    def _detect_deep_access_path(self):
        """检测项目3: 隐私政策访问路径过深"""
        try:
            # 模拟用户点击路径来访问隐私政策
            click_count = self._count_clicks_to_privacy_policy()
            
            if click_count > 4:
                self._add_violation(
                    violation_type="deep_access_path",
                    description=f"访问隐私政策需要超过4次点击（实际需要{click_count}次）",
                    evidence={
                        "click_count": click_count,
                        "max_allowed": 4,
                        "access_path": self._get_access_path()
                    },
                    severity="medium",
                    compliance_item=3
                )
                
        except Exception as e:
            logger.error(f"检测访问路径时发生错误: {e}")
    
    def _detect_readability_issues(self):
        """检测项目4: 隐私政策难以阅读"""
        try:
            if not self.privacy_policy_content:
                return
                
            issues = []
            
            # 检查字体大小（通过UI元素分析）
            font_size_issues = self._check_font_size()
            if font_size_issues:
                issues.extend(font_size_issues)
            
            # 检查颜色对比度
            color_issues = self._check_color_contrast()
            if color_issues:
                issues.extend(color_issues)
            
            # 检查是否提供简体中文版
            if not self._has_chinese_version():
                issues.append("未提供简体中文版本")
            
            if issues:
                self._add_violation(
                    violation_type="readability_issues",
                    description="隐私政策存在阅读困难问题",
                    evidence={
                        "issues": issues,
                        "content_length": len(self.privacy_policy_content)
                    },
                    severity="medium",
                    compliance_item=4
                )
                
        except Exception as e:
            logger.error(f"检测可读性问题时发生错误: {e}")
    
    def _detect_incomplete_information_list(self):
        """检测项目5: 未逐项说明收集信息"""
        try:
            if not self.privacy_policy_content:
                return
                
            # 检查是否使用模糊表述
            vague_terms = ["等", "例如", "包括但不限于", "等等", "其他"]
            found_vague_terms = []
            
            for term in vague_terms:
                if term in self.privacy_policy_content:
                    found_vague_terms.append(term)
            
            # 检查是否明确列出收集的信息类型
            required_info_types = [
                "设备信息", "位置信息", "联系人", "通话记录", 
                "短信", "相册", "麦克风", "摄像头"
            ]
            
            missing_info_types = []
            for info_type in required_info_types:
                if info_type not in self.privacy_policy_content:
                    missing_info_types.append(info_type)
            
            if found_vague_terms or len(missing_info_types) > len(required_info_types) * 0.5:
                self._add_violation(
                    violation_type="incomplete_information_list",
                    description="未逐一列出APP及第三方SDK收集个人信息的目的、方式、范围",
                    evidence={
                        "vague_terms_found": found_vague_terms,
                        "missing_info_types": missing_info_types,
                        "total_required_types": len(required_info_types)
                    },
                    severity="high",
                    compliance_item=5
                )
                
        except Exception as e:
            logger.error(f"检测信息列表完整性时发生错误: {e}")
    
    def _detect_no_change_notification(self):
        """检测项目6: 变更规则未通知用户"""
        try:
            # 检查是否有版本更新机制
            has_update_mechanism = self._check_update_mechanism()
            
            # 检查是否有变更通知功能
            has_change_notification = self._check_change_notification()
            
            if not has_update_mechanism or not has_change_notification:
                self._add_violation(
                    violation_type="no_change_notification",
                    description="缺少隐私政策变更通知机制",
                    evidence={
                        "has_update_mechanism": has_update_mechanism,
                        "has_change_notification": has_change_notification
                    },
                    severity="medium",
                    compliance_item=6
                )
                
        except Exception as e:
            logger.error(f"检测变更通知时发生错误: {e}")
    
    def _detect_permission_purpose_missing(self):
        """检测项目7: 敏感权限未同步告知目的"""
        try:
            # 这个检测需要与权限检测模块配合
            # 检查权限申请时是否同步说明用途
            permission_requests = self._get_permission_requests()
            
            for request in permission_requests:
                if not request.get('purpose_explained', False):
                    self._add_violation(
                        violation_type="permission_purpose_missing",
                        description=f"申请{request['permission']}权限时未同步说明用途",
                        evidence={
                            "permission": request['permission'],
                            "request_time": request['timestamp'],
                            "purpose_explained": False
                        },
                        severity="high",
                        compliance_item=7
                    )
                    
        except Exception as e:
            logger.error(f"检测权限用途说明时发生错误: {e}")
    
    def _detect_complex_language(self):
        """检测项目8: 规则内容晦涩难懂"""
        try:
            if not self.privacy_policy_content:
                return
                
            # 检查专业术语密度
            technical_terms = [
                "API", "SDK", "Cookie", "Token", "加密", "哈希",
                "算法", "协议", "接口", "数据库", "服务器"
            ]
            
            term_count = 0
            for term in technical_terms:
                term_count += self.privacy_policy_content.count(term)
            
            # 检查句子长度
            sentences = re.split(r'[。！？]', self.privacy_policy_content)
            long_sentences = [s for s in sentences if len(s) > 100]
            
            # 检查段落长度
            paragraphs = self.privacy_policy_content.split('\n')
            long_paragraphs = [p for p in paragraphs if len(p) > 500]
            
            complexity_score = (
                term_count * 2 + 
                len(long_sentences) * 3 + 
                len(long_paragraphs) * 5
            )
            
            if complexity_score > 50:  # 阈值可调整
                self._add_violation(
                    violation_type="complex_language",
                    description="隐私政策使用大量专业术语、冗长表述，用户难以理解",
                    evidence={
                        "technical_terms_count": term_count,
                        "long_sentences_count": len(long_sentences),
                        "long_paragraphs_count": len(long_paragraphs),
                        "complexity_score": complexity_score
                    },
                    severity="medium",
                    compliance_item=8
                )
                
        except Exception as e:
            logger.error(f"检测语言复杂度时发生错误: {e}")
    
    def _add_violation(self, violation_type: str, description: str, 
                      evidence: Dict[str, Any], severity: str, compliance_item: int):
        """添加违规记录"""
        violation = {
            "id": len(self.violations) + 1,
            "type": violation_type,
            "description": description,
            "evidence": evidence,
            "severity": severity,
            "compliance_item": compliance_item,
            "timestamp": time.time(),
            "category": "privacy_policy"
        }
        
        self.violations.append(violation)
        logger.warning(f"发现隐私政策违规: {description}")
    
    # 辅助方法（简化实现，实际需要更复杂的UI分析）
    def _search_privacy_policy(self) -> bool:
        """搜索隐私政策"""
        # 实际实现需要UI自动化测试
        return False
    
    def _count_clicks_to_privacy_policy(self) -> int:
        """计算访问隐私政策的点击次数"""
        # 实际实现需要UI自动化测试
        return 5
    
    def _get_access_path(self) -> List[str]:
        """获取访问路径"""
        return ["设置", "关于", "法律信息", "隐私政策"]
    
    def _check_font_size(self) -> List[str]:
        """检查字体大小"""
        return []
    
    def _check_color_contrast(self) -> List[str]:
        """检查颜色对比度"""
        return []
    
    def _has_chinese_version(self) -> bool:
        """检查是否有中文版本"""
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', self.privacy_policy_content)
        return len(chinese_chars) > 100
    
    def _check_update_mechanism(self) -> bool:
        """检查更新机制"""
        return False
    
    def _check_change_notification(self) -> bool:
        """检查变更通知"""
        return False
    
    def _get_permission_requests(self) -> List[Dict[str, Any]]:
        """获取权限申请记录"""
        return []
