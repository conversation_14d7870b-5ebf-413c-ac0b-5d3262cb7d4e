#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户权利保障检测模块
检测项目26-30：用户数据更正、删除、注销等权利保障检测
"""

import time
import re
from typing import List, Dict, Any, Optional
from ..utils.logger import get_logger

logger = get_logger(__name__)

class UserRightsDetector:
    """用户权利保障合规检测器"""
    
    def __init__(self, frida_session, device, config):
        self.session = frida_session
        self.device = device
        self.config = config
        self.violations = []
        self.ui_elements = []
        self.user_operations = []
        self.start_time = time.time()
        
        # 用户权利相关的关键词
        self.rights_keywords = {
            'correction': ['修改', '更正', '编辑', '修改资料', '个人信息修改'],
            'deletion': ['删除', '清除', '移除', '删除数据', '清空数据'],
            'cancellation': ['注销', '销户', '删除账号', '注销账户', '账户注销'],
            'complaint': ['投诉', '举报', '反馈', '客服', '意见反馈', '联系我们']
        }
        
        # 不合理条件关键词
        self.unreasonable_conditions = [
            '手持身份证', '身份证照片', '银行卡信息', '户口本',
            '工作证明', '收入证明', '学历证明', '房产证明'
        ]
    
    def detect_all(self) -> List[Dict[str, Any]]:
        """执行所有用户权利保障相关检测"""
        logger.info("开始用户权利保障合规检测")
        
        # 检测项目26: 未提供更正/删除/注销功能
        self._detect_missing_user_rights_functions()
        
        # 检测项目27: 设置注销/更正不合理条件
        self._detect_unreasonable_conditions()
        
        # 检测项目28: 未及时响应用户操作
        self._detect_delayed_response()
        
        # 检测项目29: 前端操作未同步后台
        self._detect_frontend_backend_mismatch()
        
        # 检测项目30: 未建立投诉渠道或超期处理
        self._detect_missing_complaint_channel()
        
        logger.info(f"用户权利保障检测完成，发现 {len(self.violations)} 个违规项")
        return self.violations
    
    def _detect_missing_user_rights_functions(self):
        """检测项目26: 未提供更正/删除/注销功能"""
        try:
            # 搜索用户权利相关功能
            found_functions = {
                'correction': False,
                'deletion': False,
                'cancellation': False
            }
            
            # 分析UI元素和菜单项
            ui_text_content = self._get_all_ui_text()
            
            for function_type, keywords in self.rights_keywords.items():
                if function_type in ['correction', 'deletion', 'cancellation']:
                    for keyword in keywords:
                        if keyword in ui_text_content:
                            found_functions[function_type] = True
                            break
            
            # 检查功能可用性
            missing_functions = []
            for function_type, found in found_functions.items():
                if not found:
                    missing_functions.append(function_type)
                else:
                    # 检查功能是否真正可用
                    if not self._test_function_availability(function_type):
                        missing_functions.append(f"{function_type}_not_functional")
            
            if missing_functions:
                function_names = {
                    'correction': '修改资料',
                    'deletion': '删除数据',
                    'cancellation': '注销账号'
                }
                
                missing_names = [function_names.get(f.replace('_not_functional', ''), f) 
                               for f in missing_functions]
                
                self._add_violation(
                    violation_type="missing_user_rights_functions",
                    description=f"APP内缺少用户权利功能: {', '.join(missing_names)}",
                    evidence={
                        "missing_functions": missing_functions,
                        "searched_keywords": self.rights_keywords,
                        "ui_content_length": len(ui_text_content)
                    },
                    severity="high",
                    compliance_item=26
                )
                
        except Exception as e:
            logger.error(f"检测用户权利功能时发生错误: {e}")
    
    def _detect_unreasonable_conditions(self):
        """检测项目27: 设置注销/更正不合理条件"""
        try:
            unreasonable_requirements = []
            
            # 检查注销和更正流程中的要求
            for operation_type in ['cancellation', 'correction']:
                requirements = self._analyze_operation_requirements(operation_type)
                
                for requirement in requirements:
                    requirement_text = requirement.get('text', '').lower()
                    
                    # 检查是否包含不合理条件
                    for unreasonable_condition in self.unreasonable_conditions:
                        if unreasonable_condition in requirement_text:
                            unreasonable_requirements.append({
                                'operation_type': operation_type,
                                'requirement': requirement,
                                'unreasonable_condition': unreasonable_condition
                            })
                    
                    # 检查是否要求过多历史信息
                    if self._requires_excessive_history(requirement_text):
                        unreasonable_requirements.append({
                            'operation_type': operation_type,
                            'requirement': requirement,
                            'issue': '要求过多历史记录'
                        })
            
            if unreasonable_requirements:
                self._add_violation(
                    violation_type="unreasonable_conditions",
                    description="注销或更正功能设置了不合理的条件",
                    evidence={
                        "unreasonable_requirements": unreasonable_requirements,
                        "count": len(unreasonable_requirements)
                    },
                    severity="medium",
                    compliance_item=27
                )
                
        except Exception as e:
            logger.error(f"检测不合理条件时发生错误: {e}")
    
    def _detect_delayed_response(self):
        """检测项目28: 未及时响应用户操作"""
        try:
            delayed_operations = []
            
            # 分析用户操作的响应时间
            for operation in self.user_operations:
                operation_type = operation.get('type')
                if operation_type in ['correction_request', 'deletion_request', 'cancellation_request']:
                    
                    response_time = operation.get('response_time')
                    processing_time = operation.get('processing_time')
                    
                    # 检查是否超过15个工作日（假设为21天）
                    max_processing_days = 21
                    max_processing_seconds = max_processing_days * 24 * 3600
                    
                    if processing_time and processing_time > max_processing_seconds:
                        delayed_operations.append({
                            'operation': operation,
                            'processing_days': processing_time / (24 * 3600),
                            'max_allowed_days': max_processing_days
                        })
                    
                    # 检查是否没有明确承诺时限
                    if not operation.get('has_time_commitment'):
                        delayed_operations.append({
                            'operation': operation,
                            'issue': '未明确承诺处理时限'
                        })
            
            if delayed_operations:
                self._add_violation(
                    violation_type="delayed_response",
                    description="用户操作请求超过15个工作日未处理或未明确承诺时限",
                    evidence={
                        "delayed_operations": delayed_operations,
                        "count": len(delayed_operations)
                    },
                    severity="medium",
                    compliance_item=28
                )
                
        except Exception as e:
            logger.error(f"检测响应延迟时发生错误: {e}")
    
    def _detect_frontend_backend_mismatch(self):
        """检测项目29: 前端操作未同步后台"""
        try:
            mismatch_cases = []
            
            # 检查删除/注销操作的前后端一致性
            for operation in self.user_operations:
                if operation.get('type') in ['deletion', 'cancellation']:
                    
                    # 检查前端显示的状态
                    frontend_status = operation.get('frontend_status')
                    
                    # 检查后台实际状态
                    backend_status = self._check_backend_status(operation)
                    
                    if frontend_status != backend_status:
                        mismatch_cases.append({
                            'operation': operation,
                            'frontend_status': frontend_status,
                            'backend_status': backend_status,
                            'mismatch_type': self._classify_mismatch(frontend_status, backend_status)
                        })
                    
                    # 检查数据是否真正删除
                    if operation.get('type') == 'deletion':
                        data_still_exists = self._check_data_existence(operation)
                        if data_still_exists:
                            mismatch_cases.append({
                                'operation': operation,
                                'issue': '前端显示已删除但后台数据仍存在'
                            })
            
            if mismatch_cases:
                self._add_violation(
                    violation_type="frontend_backend_mismatch",
                    description="用户执行删除/注销后，APP后台仍保留数据",
                    evidence={
                        "mismatch_cases": mismatch_cases,
                        "count": len(mismatch_cases)
                    },
                    severity="high",
                    compliance_item=29
                )
                
        except Exception as e:
            logger.error(f"检测前后端不一致时发生错误: {e}")
    
    def _detect_missing_complaint_channel(self):
        """检测项目30: 未建立投诉渠道或超期处理"""
        try:
            complaint_issues = []
            
            # 检查是否有投诉举报渠道
            complaint_channels = self._find_complaint_channels()
            
            if not complaint_channels:
                complaint_issues.append({
                    'issue': '未公布投诉举报渠道',
                    'searched_areas': ['设置', '帮助', '关于', '联系我们', '客服']
                })
            else:
                # 检查投诉渠道的可用性
                for channel in complaint_channels:
                    if not self._test_complaint_channel(channel):
                        complaint_issues.append({
                            'issue': '投诉渠道不可用',
                            'channel': channel
                        })
            
            # 检查投诉处理时效
            complaint_responses = self._analyze_complaint_responses()
            for response in complaint_responses:
                response_time = response.get('response_time_days', 0)
                if response_time > 15:  # 超过15个工作日
                    complaint_issues.append({
                        'issue': '投诉处理超期',
                        'response_time_days': response_time,
                        'max_allowed_days': 15,
                        'complaint': response
                    })
            
            if complaint_issues:
                self._add_violation(
                    violation_type="missing_complaint_channel",
                    description="未建立投诉渠道或投诉处理超期",
                    evidence={
                        "complaint_issues": complaint_issues,
                        "count": len(complaint_issues)
                    },
                    severity="medium",
                    compliance_item=30
                )
                
        except Exception as e:
            logger.error(f"检测投诉渠道时发生错误: {e}")
    
    def _add_violation(self, violation_type: str, description: str, 
                      evidence: Dict[str, Any], severity: str, compliance_item: int):
        """添加违规记录"""
        violation = {
            "id": len(self.violations) + 1,
            "type": violation_type,
            "description": description,
            "evidence": evidence,
            "severity": severity,
            "compliance_item": compliance_item,
            "timestamp": time.time(),
            "category": "user_rights"
        }
        
        self.violations.append(violation)
        logger.warning(f"发现用户权利违规: {description}")
    
    # 辅助方法（简化实现）
    def _get_all_ui_text(self) -> str:
        """获取所有UI文本内容"""
        # 实际实现需要UI遍历和文本提取
        return ""
    
    def _test_function_availability(self, function_type: str) -> bool:
        """测试功能可用性"""
        # 实际实现需要UI自动化测试
        return True
    
    def _analyze_operation_requirements(self, operation_type: str) -> List[Dict[str, Any]]:
        """分析操作要求"""
        # 实际实现需要流程分析
        return []
    
    def _requires_excessive_history(self, requirement_text: str) -> bool:
        """检查是否要求过多历史信息"""
        excessive_indicators = ['10条', '历史记录', '详细信息', '完整填写']
        return any(indicator in requirement_text for indicator in excessive_indicators)
    
    def _check_backend_status(self, operation: Dict[str, Any]) -> str:
        """检查后台状态"""
        # 实际实现需要API调用或数据库查询
        return "unknown"
    
    def _classify_mismatch(self, frontend_status: str, backend_status: str) -> str:
        """分类不一致类型"""
        if frontend_status == "deleted" and backend_status == "active":
            return "fake_deletion"
        elif frontend_status == "cancelled" and backend_status == "active":
            return "fake_cancellation"
        else:
            return "status_mismatch"
    
    def _check_data_existence(self, operation: Dict[str, Any]) -> bool:
        """检查数据是否仍存在"""
        # 实际实现需要数据验证
        return False
    
    def _find_complaint_channels(self) -> List[Dict[str, Any]]:
        """查找投诉渠道"""
        # 实际实现需要UI搜索
        return []
    
    def _test_complaint_channel(self, channel: Dict[str, Any]) -> bool:
        """测试投诉渠道可用性"""
        # 实际实现需要功能测试
        return True
    
    def _analyze_complaint_responses(self) -> List[Dict[str, Any]]:
        """分析投诉响应"""
        # 实际实现需要历史数据分析
        return []
