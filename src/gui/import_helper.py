#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入辅助模块
用于解决GUI中导入改进检测器的问题
"""

import sys
import os

def import_improved_detector():
    """导入改进的检测器"""
    try:
        # 获取项目根目录
        current_file = os.path.abspath(__file__)
        gui_dir = os.path.dirname(current_file)
        src_dir = os.path.dirname(gui_dir)
        project_root = os.path.dirname(src_dir)
        
        # 添加路径
        paths_to_add = [project_root, src_dir]
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.insert(0, path)
        
        # 尝试导入
        try:
            from improved_detector import ImprovedPrivacyDetector
            return ImprovedPrivacyDetector, True
        except ImportError:
            from src.improved_detector import ImprovedPrivacyDetector
            return ImprovedPrivacyDetector, True
            
    except ImportError as e:
        print(f"导入改进检测器失败: {e}")
        return None, False

# 执行导入
ImprovedPrivacyDetector, IMPROVED_DETECTOR_AVAILABLE = import_improved_detector()
