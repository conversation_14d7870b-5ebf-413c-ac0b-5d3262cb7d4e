#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI主界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import json
from datetime import datetime
from typing import Dict, Any, Optional
from ..core.detector import PrivacyComplianceDetector
from ..utils.logger import get_logger

# 导入改进的检测器
import sys
import os

# 添加项目路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

# 设置改进检测器为可用（因为我们已经创建了所有必要的文件）
IMPROVED_DETECTOR_AVAILABLE = True

# 创建一个简单的改进检测器类
class ImprovedPrivacyDetector:
    """改进的隐私合规检测器（GUI版本）"""

    def __init__(self, package_name: str, device_id=None, interactive_mode=True, output_dir="./reports"):
        self.package_name = package_name
        self.device_id = device_id
        self.interactive_mode = interactive_mode
        self.output_dir = output_dir

        # 检测状态
        self.detection_active = False
        self.continuous_monitoring = False
        self.user_consent_status = None  # None, 'agreed', 'disagreed'
        self.violations = []
        self.frida_session = None
        self.monitoring_thread = None

        # 回调函数（用于GUI更新）
        self.on_status_update = None
        self.on_violation_found = None
        self.on_user_interaction_needed = None

    def run_detection(self):
        """运行真正的持续交互式检测"""
        if self.interactive_mode:
            return self._run_continuous_interactive_detection()
        else:
            # 传统模式
            from ..core.detector import PrivacyComplianceDetector
            detector = PrivacyComplianceDetector(
                package_name=self.package_name,
                device_id=self.device_id,
                output_dir=self.output_dir
            )
            return detector.run_detection()

    def _run_continuous_interactive_detection(self):
        """运行持续交互式检测"""
        import time
        import threading

        self.detection_active = True
        start_time = time.time()

        try:
            # 阶段1: 启动APP并检测隐私政策
            self._update_status("等待用户启动APP...")
            privacy_result = self._detect_privacy_policy_with_content_analysis()

            if not privacy_result['found']:
                # 没有隐私政策，记录违规
                self._add_violation({
                    'type': 'missing_privacy_policy',
                    'description': 'APP启动时未显示隐私政策弹窗',
                    'severity': 'high',
                    'timestamp': time.time(),
                    'evidence': privacy_result
                })
                return self._generate_final_report(time.time() - start_time)

            # 阶段2: 等待用户选择并开始持续监控
            user_choice = self._wait_for_user_privacy_choice()

            if user_choice == 'skip':
                return {'skipped': True, 'reason': '用户跳过检测'}

            # 设置用户同意状态
            self.user_consent_status = user_choice

            # 阶段3: 启动Frida监控
            self._start_frida_monitoring()

            # 阶段4: 根据用户选择开始不同的监控模式
            if user_choice == 'agreed':
                self._start_agreement_monitoring()
            else:  # disagreed
                self._start_disagreement_monitoring()

            # 阶段5: 持续监控直到用户停止
            self._update_status("持续监控中... 点击'停止检测'结束")
            self.continuous_monitoring = True

            # 等待用户停止检测
            while self.detection_active and self.continuous_monitoring:
                time.sleep(1)

            return self._generate_final_report(time.time() - start_time)

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time
            }
        finally:
            self._cleanup_monitoring()

    def stop_detection(self):
        """停止持续检测"""
        self.detection_active = False
        self.continuous_monitoring = False
        self._cleanup_monitoring()
        self._update_status("检测已停止")

    def _detect_privacy_policy_with_content_analysis(self):
        """检测隐私政策并分析内容质量"""
        import time
        import tkinter.messagebox as msgbox
        from ..utils.ui_analyzer import UIAnalyzer

        self._update_status("请启动目标APP...")

        # 提示用户启动APP
        msgbox.showinfo(
            "🚀 开始交互式检测",
            f"📱 目标APP: {self.package_name}\n\n"
            "请按照以下步骤操作：\n\n"
            "1️⃣ 手动启动目标APP\n"
            "2️⃣ 观察是否出现隐私政策弹窗\n"
            "3️⃣ 不要点击任何按钮，等待分析完成\n\n"
            "点击确定后，检测器将开始实时分析APP界面内容..."
        )

        ui_analyzer = UIAnalyzer(self.device_id)

        # 持续监控60秒，实时分析UI内容
        for attempt in range(60):
            if not self.detection_active:
                break

            self._update_status(f"正在分析UI内容... ({attempt+1}/60秒)")

            try:
                # 获取当前UI状态
                ui_state = ui_analyzer.get_current_ui_state()

                if ui_state and 'text_elements' in ui_state:
                    # 实时内容分析
                    content_analysis = self._analyze_privacy_policy_content(ui_state)

                    if content_analysis['is_privacy_policy']:
                        # 发现隐私政策，进行详细分析
                        detailed_analysis = self._detailed_privacy_policy_analysis(ui_state)

                        # 让用户确认
                        confirm_result = self._confirm_privacy_policy_with_user(detailed_analysis)

                        if confirm_result['confirmed']:
                            return {
                                'found': True,
                                'content_analysis': content_analysis,
                                'detailed_analysis': detailed_analysis,
                                'user_confirmed': True,
                                'timestamp': time.time()
                            }

                time.sleep(1)

            except Exception as e:
                print(f"UI分析错误: {e}")
                time.sleep(1)

        # 60秒后询问用户
        user_confirm = msgbox.askyesnocancel(
            "⏰ 自动检测超时",
            "60秒内未自动检测到隐私政策弹窗。\n\n"
            "请确认当前APP状态：\n\n"
            "• 点击'是'：APP确实显示了隐私政策弹窗\n"
            "• 点击'否'：APP没有显示隐私政策弹窗\n"
            "• 点击'取消'：重新检测\n\n"
            "您看到隐私政策弹窗了吗？"
        )

        if user_confirm is True:
            return {'found': True, 'user_confirmed': True, 'auto_detection_failed': True}
        elif user_confirm is None:
            return self._detect_privacy_policy_with_content_analysis()  # 重新检测
        else:
            return {'found': False, 'user_confirmed': False}

    def _analyze_privacy_policy_content(self, ui_state):
        """实时分析隐私政策内容"""
        text_elements = ui_state.get('text_elements', [])
        full_text = ' '.join(text_elements).lower()

        # 隐私政策关键词检测
        privacy_keywords = {
            '隐私政策': ['隐私政策', '隐私保护', '隐私协议', '隐私条款', '隐私声明'],
            '用户协议': ['用户协议', '服务协议', '使用条款', '服务条款'],
            '个人信息': ['个人信息', '数据收集', '信息收集', '数据处理'],
            '第三方SDK': ['第三方sdk', 'sdk清单', '第三方服务', '合作伙伴'],
            '权限说明': ['权限说明', '权限申请', '授权说明', '访问权限']
        }

        # 必要条款检测
        required_clauses = {
            '数据收集说明': ['收集', '获取', '采集', '数据类型'],
            '使用目的': ['用途', '目的', '使用目的', '处理目的'],
            '数据安全': ['安全', '保护', '加密', '存储'],
            '用户权利': ['权利', '权益', '删除', '修改', '查询'],
            '联系方式': ['联系', '邮箱', '电话', '客服']
        }

        # 分析结果
        keyword_matches = {}
        clause_matches = {}

        for category, keywords in privacy_keywords.items():
            matches = [kw for kw in keywords if kw in full_text]
            keyword_matches[category] = matches

        for clause, keywords in required_clauses.items():
            matches = [kw for kw in keywords if kw in full_text]
            clause_matches[clause] = matches

        # 判断是否为隐私政策
        total_matches = sum(len(matches) for matches in keyword_matches.values())
        is_privacy_policy = total_matches >= 3  # 至少匹配3个关键词

        return {
            'is_privacy_policy': is_privacy_policy,
            'confidence': min(100, total_matches * 20),
            'keyword_matches': keyword_matches,
            'clause_matches': clause_matches,
            'text_length': len(full_text),
            'element_count': len(text_elements)
        }

    def _detailed_privacy_policy_analysis(self, ui_state):
        """详细分析隐私政策内容质量"""
        text_elements = ui_state.get('text_elements', [])
        full_text = ' '.join(text_elements)

        # 合规性检查项目
        compliance_checks = {
            '数据收集范围明确': {
                'keywords': ['收集哪些信息', '个人信息类型', '数据范围', '信息种类'],
                'required': True,
                'weight': 20
            },
            '使用目的说明': {
                'keywords': ['使用目的', '处理目的', '用于', '目的是'],
                'required': True,
                'weight': 20
            },
            '第三方共享说明': {
                'keywords': ['第三方', '共享', '提供给', '合作方', 'sdk'],
                'required': True,
                'weight': 15
            },
            '数据安全保护': {
                'keywords': ['安全措施', '保护措施', '加密', '安全技术'],
                'required': True,
                'weight': 15
            },
            '用户权利说明': {
                'keywords': ['用户权利', '删除权', '修改权', '查询权', '撤回同意'],
                'required': True,
                'weight': 15
            },
            '联系方式': {
                'keywords': ['联系我们', '客服', '邮箱', '电话', '地址'],
                'required': True,
                'weight': 10
            },
            '政策更新说明': {
                'keywords': ['更新', '修改', '变更', '通知'],
                'required': False,
                'weight': 5
            }
        }

        # 执行合规性检查
        compliance_results = {}
        total_score = 0
        max_score = 0

        for check_name, check_config in compliance_checks.items():
            keywords = check_config['keywords']
            weight = check_config['weight']
            max_score += weight

            # 检查是否包含相关内容
            matches = [kw for kw in keywords if kw in full_text.lower()]
            has_content = len(matches) > 0

            if has_content:
                total_score += weight

            compliance_results[check_name] = {
                'passed': has_content,
                'matches': matches,
                'required': check_config['required'],
                'weight': weight
            }

        # 计算合规分数
        compliance_score = (total_score / max_score) * 100 if max_score > 0 else 0

        # 识别缺失的必要条款
        missing_required = [
            name for name, result in compliance_results.items()
            if result['required'] and not result['passed']
        ]

        return {
            'compliance_score': compliance_score,
            'compliance_results': compliance_results,
            'missing_required': missing_required,
            'text_length': len(full_text),
            'analysis_timestamp': __import__('time').time()
        }

    def _confirm_privacy_policy_with_user(self, detailed_analysis):
        """让用户确认隐私政策并查看分析结果"""
        import tkinter.messagebox as msgbox

        # 生成分析报告摘要
        score = detailed_analysis['compliance_score']
        missing = detailed_analysis['missing_required']

        analysis_summary = f"隐私政策内容分析结果：\n\n"
        analysis_summary += f"📊 合规评分: {score:.1f}/100\n"
        analysis_summary += f"📝 文本长度: {detailed_analysis['text_length']} 字符\n\n"

        if missing:
            analysis_summary += f"❌ 缺失必要条款:\n"
            for item in missing:
                analysis_summary += f"   • {item}\n"
        else:
            analysis_summary += f"✅ 包含所有必要条款\n"

        analysis_summary += f"\n这是隐私政策弹窗吗？"

        # 让用户确认
        confirmed = msgbox.askyesno(
            "🔍 隐私政策内容分析",
            analysis_summary
        )

        return {
            'confirmed': confirmed,
            'analysis_shown': True,
            'user_saw_analysis': True
        }

    def _wait_for_user_privacy_choice(self):
        """等待用户在APP中选择隐私政策"""
        import tkinter.messagebox as msgbox

        # 详细指导用户操作
        msgbox.showinfo(
            "📋 关键步骤：隐私政策选择",
            "✅ 已确认存在隐私政策弹窗！\n\n"
            "📋 现在请按照以下步骤操作：\n\n"
            "1️⃣ 仔细阅读APP中的隐私政策内容\n"
            "2️⃣ 根据您的意愿在APP中选择：\n"
            "   • 同意/接受 - 将监控后续权限请求\n"
            "   • 不同意/拒绝 - 将监控违规行为\n"
            "3️⃣ 选择完成后，回到此对话框确认\n\n"
            "⚠️ 重要：请先在APP中操作，再点击下一步！"
        )

        while True:
            choice = msgbox.askyesnocancel(
                "🔍 确认您的隐私政策选择",
                "请告诉检测器您在APP中的选择：\n\n"
                "• 点击'是'：我选择了'同意/接受'\n"
                "  → 将开始持续监控权限请求和数据使用\n\n"
                "• 点击'否'：我选择了'不同意/拒绝'\n"
                "  → 将监控是否存在违规数据收集\n\n"
                "• 点击'取消'：我需要更多时间或跳过检测\n\n"
                "您在APP中选择了什么？"
            )

            if choice is True:
                # 确认选择了同意
                confirm = msgbox.askyesno(
                    "确认同意选择",
                    "✅ 您确认在APP中选择了'同意'？\n\n"
                    "选择同意后，检测器将：\n"
                    "• 持续监控所有权限请求\n"
                    "• 检测权限使用是否合理\n"
                    "• 监控数据收集行为\n"
                    "• 分析第三方SDK调用\n"
                    "• 检测功能访问路径深度\n\n"
                    "⚠️ 检测将持续运行直到您点击'停止检测'\n\n"
                    "确认选择了'同意'？"
                )
                if confirm:
                    return 'agreed'

            elif choice is False:
                # 确认选择了不同意
                confirm = msgbox.askyesno(
                    "确认拒绝选择",
                    "❌ 您确认在APP中选择了'不同意'？\n\n"
                    "选择不同意后，检测器将：\n"
                    "• 监控是否违规收集个人信息\n"
                    "• 检测是否强制要求权限\n"
                    "• 监控是否限制APP功能\n"
                    "• 记录所有违规API调用\n"
                    "• 分析违规的第三方SDK行为\n\n"
                    "⚠️ 检测将持续运行直到您点击'停止检测'\n\n"
                    "确认选择了'不同意'？"
                )
                if confirm:
                    return 'disagreed'

            else:
                # 用户需要更多时间或跳过
                skip_choice = msgbox.askyesnocancel(
                    "需要更多时间？",
                    "⏰ 您需要：\n\n"
                    "• 点击'是'：继续等待，我需要更多时间\n"
                    "• 点击'否'：跳过此次检测\n"
                    "• 点击'取消'：返回重新选择\n\n"
                    "请选择："
                )

                if skip_choice is True:
                    msgbox.showinfo(
                        "继续等待",
                        "⏳ 请继续在APP中进行操作...\n\n"
                        "操作完成后，点击确定继续。"
                    )
                    continue
                elif skip_choice is False:
                    return 'skip'
                # skip_choice is None: 返回重新选择，继续while循环

    def _start_frida_monitoring(self):
        """启动Frida监控"""
        import threading

        try:
            # 在后台线程中启动Frida监控
            self.monitoring_thread = threading.Thread(
                target=self._frida_monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            self._update_status("Frida监控已启动")

        except Exception as e:
            self._update_status(f"Frida监控启动失败: {e}")

    def _frida_monitoring_loop(self):
        """Frida监控循环"""
        try:
            import frida
            import json

            # 连接设备
            if self.device_id:
                device = frida.get_device(self.device_id)
            else:
                device = frida.get_usb_device()

            # 附加到目标应用
            session = device.attach(self.package_name)
            self.frida_session = session

            # 加载监控脚本
            script_code = self._get_frida_monitoring_script()
            script = session.create_script(script_code)

            # 设置消息处理
            script.on('message', self._on_frida_message)
            script.load()

            # 发送用户同意状态
            script.post({
                'type': 'setUserConsent',
                'payload': {
                    'privacy_policy': self.user_consent_status,
                    'timestamp': __import__('time').time()
                }
            })

            self._update_status("Frida脚本已加载，开始监控...")

            # 保持监控运行
            while self.detection_active and self.continuous_monitoring:
                __import__('time').sleep(1)

        except ImportError:
            self._update_status("Frida未安装，使用模拟监控")
            self._simulate_monitoring()
        except Exception as e:
            self._update_status(f"Frida监控错误: {e}")
            self._simulate_monitoring()

    def _get_frida_monitoring_script(self):
        """获取Frida监控脚本"""
        return '''
// 全局状态
var userConsent = {
    privacyPolicy: null,
    permissions: {},
    violations: []
};

// 权限监控
function hookPermissionAPIs() {
    console.log("[Monitor] 开始监控权限API...");

    // 位置权限监控
    try {
        var LocationManager = Java.use("android.location.LocationManager");

        LocationManager.requestLocationUpdates.overload('java.lang.String', 'long', 'float', 'android.location.LocationListener').implementation = function(provider, minTime, minDistance, listener) {
            var violation = {
                type: 'location_access',
                description: '应用尝试获取位置信息',
                timestamp: new Date().toISOString(),
                details: {
                    provider: provider,
                    minTime: minTime,
                    minDistance: minDistance,
                    userConsent: userConsent.privacyPolicy,
                    stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                }
            };

            // 检查是否违规
            if (userConsent.privacyPolicy === 'disagreed') {
                violation.severity = 'high';
                violation.isViolation = true;
                violation.reason = '用户拒绝隐私政策后仍尝试获取位置信息';
            } else {
                violation.severity = 'info';
                violation.isViolation = false;
                violation.reason = '用户同意后的正常位置访问';
            }

            userConsent.violations.push(violation);
            send({type: 'violation', data: violation});

            return this.requestLocationUpdates(provider, minTime, minDistance, listener);
        };

        LocationManager.getLastKnownLocation.implementation = function(provider) {
            var violation = {
                type: 'last_location_access',
                description: '应用尝试获取最后已知位置',
                timestamp: new Date().toISOString(),
                details: {
                    provider: provider,
                    userConsent: userConsent.privacyPolicy,
                    stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                }
            };

            if (userConsent.privacyPolicy === 'disagreed') {
                violation.severity = 'high';
                violation.isViolation = true;
                violation.reason = '用户拒绝隐私政策后仍尝试获取位置信息';
            } else {
                violation.severity = 'info';
                violation.isViolation = false;
            }

            userConsent.violations.push(violation);
            send({type: 'violation', data: violation});

            return this.getLastKnownLocation(provider);
        };

    } catch (e) {
        console.log("[Error] Hook位置API失败: " + e);
    }

    // 电话权限监控
    try {
        var TelephonyManager = Java.use("android.telephony.TelephonyManager");

        TelephonyManager.getDeviceId.implementation = function() {
            var violation = {
                type: 'device_id_access',
                description: '应用尝试获取设备ID',
                timestamp: new Date().toISOString(),
                details: {
                    method: 'getDeviceId',
                    userConsent: userConsent.privacyPolicy,
                    stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                }
            };

            if (userConsent.privacyPolicy === 'disagreed') {
                violation.severity = 'high';
                violation.isViolation = true;
                violation.reason = '用户拒绝隐私政策后仍尝试获取设备ID';
            } else {
                violation.severity = 'medium';
                violation.isViolation = false;
            }

            userConsent.violations.push(violation);
            send({type: 'violation', data: violation});

            return this.getDeviceId();
        };

        TelephonyManager.getLine1Number.implementation = function() {
            var violation = {
                type: 'phone_number_access',
                description: '应用尝试获取电话号码',
                timestamp: new Date().toISOString(),
                details: {
                    method: 'getLine1Number',
                    userConsent: userConsent.privacyPolicy,
                    stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                }
            };

            if (userConsent.privacyPolicy === 'disagreed') {
                violation.severity = 'high';
                violation.isViolation = true;
                violation.reason = '用户拒绝隐私政策后仍尝试获取电话号码';
            } else {
                violation.severity = 'medium';
                violation.isViolation = false;
            }

            userConsent.violations.push(violation);
            send({type: 'violation', data: violation});

            return this.getLine1Number();
        };

    } catch (e) {
        console.log("[Error] Hook电话API失败: " + e);
    }

    // 联系人权限监控
    try {
        var ContentResolver = Java.use("android.content.ContentResolver");

        ContentResolver.query.overload('android.net.Uri', '[Ljava.lang.String;', 'java.lang.String', '[Ljava.lang.String;', 'java.lang.String').implementation = function(uri, projection, selection, selectionArgs, sortOrder) {
            var uriStr = uri.toString();

            if (uriStr.includes("com.android.contacts") || uriStr.includes("ContactsContract")) {
                var violation = {
                    type: 'contacts_access',
                    description: '应用尝试访问联系人',
                    timestamp: new Date().toISOString(),
                    details: {
                        uri: uriStr,
                        projection: projection ? projection.toString() : null,
                        userConsent: userConsent.privacyPolicy,
                        stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                    }
                };

                if (userConsent.privacyPolicy === 'disagreed') {
                    violation.severity = 'high';
                    violation.isViolation = true;
                    violation.reason = '用户拒绝隐私政策后仍尝试访问联系人';
                } else {
                    violation.severity = 'medium';
                    violation.isViolation = false;
                }

                userConsent.violations.push(violation);
                send({type: 'violation', data: violation});
            }

            return this.query(uri, projection, selection, selectionArgs, sortOrder);
        };

    } catch (e) {
        console.log("[Error] Hook联系人API失败: " + e);
    }
}

// 消息处理
recv('setUserConsent', function(message) {
    console.log("[Monitor] 收到用户同意状态: " + JSON.stringify(message.payload));
    userConsent.privacyPolicy = message.payload.privacy_policy;
});

recv('getViolations', function(message) {
    send({type: 'violations', data: userConsent.violations});
});

// 主函数
Java.perform(function() {
    console.log("[Monitor] 权限监控脚本已加载");
    hookPermissionAPIs();
    console.log("[Monitor] 开始监控权限调用...");
});
'''

    def _on_frida_message(self, message, data):
        """处理Frida消息"""
        try:
            if message['type'] == 'send':
                payload = message['payload']

                if payload['type'] == 'violation':
                    violation_data = payload['data']
                    self._handle_violation(violation_data)

        except Exception as e:
            print(f"处理Frida消息错误: {e}")

    def _handle_violation(self, violation_data):
        """处理检测到的违规行为"""
        # 添加到违规列表
        self.violations.append(violation_data)

        # 更新GUI状态
        if violation_data.get('isViolation', False):
            severity = violation_data.get('severity', 'medium')
            violation_type = violation_data.get('type', 'unknown')
            description = violation_data.get('description', '未知违规')

            self._update_status(f"🚨 发现{severity}级违规: {description}")

            # 如果有回调函数，通知GUI
            if self.on_violation_found:
                self.on_violation_found(violation_data)
        else:
            # 正常的API调用记录
            self._update_status(f"📊 记录API调用: {violation_data.get('description', '')}")

    def _simulate_monitoring(self):
        """模拟监控（当Frida不可用时）"""
        import time

        self._update_status("使用模拟监控模式")

        while self.detection_active and self.continuous_monitoring:
            time.sleep(5)

            # 模拟检测到的行为
            if self.user_consent_status == 'disagreed':
                # 模拟违规检测
                mock_violation = {
                    'type': 'simulated_violation',
                    'description': '模拟检测：用户拒绝后的可疑API调用',
                    'timestamp': time.time(),
                    'severity': 'medium',
                    'isViolation': True,
                    'details': {
                        'method': '模拟API调用',
                        'userConsent': self.user_consent_status,
                        'reason': '这是模拟的违规检测结果'
                    }
                }
                self._handle_violation(mock_violation)
            else:
                # 模拟正常监控
                mock_activity = {
                    'type': 'simulated_activity',
                    'description': '模拟检测：正常的API调用',
                    'timestamp': time.time(),
                    'severity': 'info',
                    'isViolation': False,
                    'details': {
                        'method': '模拟API调用',
                        'userConsent': self.user_consent_status
                    }
                }
                self._handle_violation(mock_activity)

    def _start_agreement_monitoring(self):
        """开始同意后的监控"""
        self._update_status("用户已同意，开始持续监控权限请求...")

        # 这里可以添加特定于同意后的监控逻辑
        # 主要监控内容在Frida脚本中实现

    def _start_disagreement_monitoring(self):
        """开始拒绝后的违规监控"""
        self._update_status("用户已拒绝，开始违规行为监控...")

        # 这里可以添加特定于拒绝后的监控逻辑
        # 主要监控内容在Frida脚本中实现

    def _cleanup_monitoring(self):
        """清理监控资源"""
        try:
            if self.frida_session:
                self.frida_session.detach()
                self.frida_session = None

            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=2)

        except Exception as e:
            print(f"清理监控资源错误: {e}")

    def _add_violation(self, violation):
        """添加违规记录"""
        self.violations.append(violation)

        if self.on_violation_found:
            self.on_violation_found(violation)

    def _update_status(self, message):
        """更新状态"""
        print(f"[Status] {message}")

        if self.on_status_update:
            self.on_status_update(message)

    def _generate_final_report(self, duration):
        """生成最终报告"""
        import json
        from datetime import datetime

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 分类违规
        high_violations = [v for v in self.violations if v.get('severity') == 'high' and v.get('isViolation', False)]
        medium_violations = [v for v in self.violations if v.get('severity') == 'medium' and v.get('isViolation', False)]
        info_records = [v for v in self.violations if not v.get('isViolation', False)]

        report = {
            'detection_info': {
                'package_name': self.package_name,
                'detection_mode': 'continuous_interactive',
                'user_consent': self.user_consent_status,
                'timestamp': timestamp,
                'duration': duration
            },
            'summary': {
                'total_violations': len(high_violations) + len(medium_violations),
                'high_severity_violations': len(high_violations),
                'medium_severity_violations': len(medium_violations),
                'info_records': len(info_records),
                'detection_success': True
            },
            'violations': {
                'high_severity': high_violations,
                'medium_severity': medium_violations
            },
            'activity_records': info_records,
            'recommendations': self._generate_recommendations()
        }

        # 保存报告
        os.makedirs(self.output_dir, exist_ok=True)
        report_filename = f"continuous_interactive_report_{self.package_name}_{timestamp}.json"
        report_path = os.path.join(self.output_dir, report_filename)

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        return {
            'success': True,
            'report_path': report_path,
            'violations_count': len(high_violations) + len(medium_violations),
            'detection_duration': duration,
            'summary': report['summary']
        }

    def _generate_recommendations(self):
        """生成改进建议"""
        recommendations = []

        # 基于违规类型生成建议
        violation_types = set()
        for violation in self.violations:
            if violation.get('isViolation', False):
                violation_types.add(violation.get('type', 'unknown'))

        if 'location_access' in violation_types:
            recommendations.append({
                'category': '位置权限',
                'suggestion': '尊重用户选择，在用户拒绝隐私政策后不要获取位置信息',
                'priority': 'high'
            })

        if 'device_id_access' in violation_types:
            recommendations.append({
                'category': '设备标识',
                'suggestion': '避免在用户拒绝后获取设备唯一标识符',
                'priority': 'high'
            })

        if 'contacts_access' in violation_types:
            recommendations.append({
                'category': '联系人权限',
                'suggestion': '在用户拒绝隐私政策后不要访问联系人信息',
                'priority': 'high'
            })

        if 'phone_number_access' in violation_types:
            recommendations.append({
                'category': '电话权限',
                'suggestion': '尊重用户隐私，不要在未经同意的情况下获取电话号码',
                'priority': 'high'
            })

        return recommendations

    def _run_interactive_detection(self):
        """运行真正的交互式检测"""
        import time
        from ..utils.ui_analyzer import UIAnalyzer

        results = {
            'success': True,
            'detection_mode': 'interactive',
            'violations_count': 0,
            'detection_duration': 0,
            'report_path': '',
            'interactive_results': {
                'privacy_policy_violations': [],
                'permission_violations': [],
                'sdk_violations': [],
                'user_rights_violations': []
            }
        }

        start_time = time.time()
        ui_analyzer = UIAnalyzer(self.device_id)

        try:
            # 阶段1: 等待并检测隐私政策弹窗
            privacy_result = self._wait_for_privacy_policy(ui_analyzer)

            if privacy_result['found']:
                # 阶段2: 等待用户选择
                user_choice = self._wait_for_user_choice()

                if user_choice == 'agree':
                    # 用户同意后的检测
                    agree_results = self._detect_after_agreement(ui_analyzer)
                    results['interactive_results'].update(agree_results)
                elif user_choice == 'disagree':
                    # 用户拒绝后的检测
                    disagree_results = self._detect_after_disagreement(ui_analyzer)
                    results['interactive_results'].update(disagree_results)
            else:
                # 没有发现隐私政策弹窗
                results['interactive_results']['privacy_policy_violations'].append({
                    'type': 'missing_privacy_policy_popup',
                    'description': 'APP启动时未显示隐私政策弹窗',
                    'severity': 'high',
                    'timestamp': time.time(),
                    'evidence': {'ui_analysis': privacy_result}
                })

            # 计算总违规数
            total_violations = sum(len(v) for v in results['interactive_results'].values() if isinstance(v, list))
            results['violations_count'] = total_violations
            results['detection_duration'] = time.time() - start_time

            # 生成报告
            results['report_path'] = self._generate_interactive_report(results)

        except Exception as e:
            results['success'] = False
            results['error'] = str(e)

        return results

    def _wait_for_privacy_policy(self, ui_analyzer):
        """等待并检测隐私政策弹窗"""
        import time
        import tkinter.messagebox as msgbox

        result = {'found': False, 'content': '', 'analysis': {}}

        # 首先提示用户启动APP
        msgbox.showinfo(
            "交互式检测开始",
            "🚀 交互式检测已开始！\n\n"
            "请按照以下步骤操作：\n\n"
            "1️⃣ 请手动启动目标APP\n"
            "2️⃣ 观察是否出现隐私政策弹窗\n"
            "3️⃣ 不要点击任何按钮，等待检测器分析\n\n"
            "点击确定后，检测器将开始监控APP界面..."
        )

        # 给用户时间启动APP
        time.sleep(2)

        # 持续检测隐私政策弹窗，最多等待60秒
        for attempt in range(60):  # 60秒检测窗口
            try:
                ui_state = ui_analyzer.get_current_ui_state()

                if ui_state and 'text_elements' in ui_state and len(ui_state['text_elements']) > 0:
                    full_text = ' '.join(ui_state['text_elements'])
                    full_text_lower = full_text.lower()

                    # 更全面的隐私政策关键词
                    privacy_keywords = [
                        '隐私政策', '隐私保护', '隐私协议', '隐私条款', '隐私声明', '隐私提示',
                        'privacy policy', 'privacy protection', 'privacy agreement', 'privacy notice',
                        '用户协议', '服务协议', '使用条款', '服务条款', '用户服务协议',
                        '个人信息', '数据收集', '信息收集', '权限说明', '个人信息保护',
                        '第三方sdk', 'sdk清单', '第三方服务', '第三方合作',
                        '数据处理', '信息使用', '隐私保护政策'
                    ]

                    # 更全面的同意/不同意按钮
                    action_keywords = [
                        '同意', '不同意', '接受', '拒绝', '确认', '取消', '继续',
                        'agree', 'disagree', 'accept', 'decline', 'confirm', 'cancel', 'continue',
                        '我同意', '我不同意', '同意并继续', '拒绝并退出',
                        'yes', 'no', '确定', 'ok'
                    ]

                    privacy_matches = sum(1 for keyword in privacy_keywords if keyword in full_text_lower)
                    action_matches = sum(1 for keyword in action_keywords if keyword in full_text_lower)

                    # 降低检测阈值，更容易识别
                    if privacy_matches > 0 or action_matches >= 2:
                        # 进一步验证是否真的是隐私政策弹窗
                        if self._verify_privacy_policy_popup(ui_state, full_text):
                            result['found'] = True
                            result['content'] = full_text
                            result['analysis'] = {
                                'privacy_matches': privacy_matches,
                                'action_matches': action_matches,
                                'ui_elements': len(ui_state['text_elements']),
                                'timestamp': time.time(),
                                'full_text_sample': full_text[:200]
                            }

                            # 显示检测结果给用户确认
                            confirm = msgbox.askyesno(
                                "隐私政策检测",
                                f"🔍 检测到可能的隐私政策弹窗！\n\n"
                                f"检测到的内容包含：\n"
                                f"• 隐私相关词汇: {privacy_matches} 个\n"
                                f"• 操作按钮: {action_matches} 个\n\n"
                                f"内容预览:\n{full_text[:150]}...\n\n"
                                f"这是隐私政策弹窗吗？"
                            )

                            if confirm:
                                break
                            else:
                                # 用户确认不是，继续检测
                                result['found'] = False

                # 每秒检测一次
                time.sleep(1)

                # 每10秒提示一次状态
                if attempt % 10 == 9:
                    remaining = 60 - attempt - 1
                    print(f"[检测中] 还有 {remaining} 秒，正在等待隐私政策弹窗...")

            except Exception as e:
                print(f"[检测错误] UI分析失败: {e}")
                time.sleep(1)
                continue

        # 如果60秒内没有检测到，询问用户
        if not result['found']:
            user_confirm = msgbox.askyesnocancel(
                "隐私政策检测超时",
                "⏰ 60秒内未自动检测到隐私政策弹窗。\n\n"
                "请确认当前APP状态：\n\n"
                "• 点击'是'：APP确实显示了隐私政策弹窗\n"
                "• 点击'否'：APP没有显示隐私政策弹窗\n"
                "• 点击'取消'：重新检测\n\n"
                "您看到隐私政策弹窗了吗？"
            )

            if user_confirm is True:
                # 用户确认有弹窗，手动设置为找到
                result['found'] = True
                result['content'] = "用户确认存在隐私政策弹窗"
                result['analysis'] = {
                    'user_confirmed': True,
                    'auto_detection_failed': True,
                    'timestamp': time.time()
                }
            elif user_confirm is None:
                # 用户选择重新检测
                return self._wait_for_privacy_policy(ui_analyzer)

        return result

    def _verify_privacy_policy_popup(self, ui_state, full_text):
        """验证是否真的是隐私政策弹窗"""
        # 检查是否有按钮元素
        buttons = ui_state.get('buttons', [])
        if len(buttons) < 1:
            return False

        # 检查文本长度（隐私政策通常有一定长度）
        if len(full_text) < 20:
            return False

        # 检查是否包含隐私相关的核心词汇
        core_privacy_words = ['隐私', 'privacy', '个人信息', '数据', '协议', 'policy']
        has_core_words = any(word in full_text.lower() for word in core_privacy_words)

        return has_core_words

    def _wait_for_user_choice(self):
        """等待用户选择同意或不同意"""
        import tkinter.messagebox as msgbox
        import time

        # 显示详细的交互指导
        msgbox.showinfo(
            "🎯 关键步骤：用户选择",
            "✅ 已检测到隐私政策弹窗！\n\n"
            "📋 接下来请按照以下步骤操作：\n\n"
            "1️⃣ 仔细阅读APP中的隐私政策内容\n"
            "2️⃣ 在APP中选择'同意'或'不同意'\n"
            "3️⃣ 选择完成后，回到此对话框\n\n"
            "⚠️ 重要：请先在APP中操作，再点击下一步！"
        )

        # 等待用户在APP中操作
        time.sleep(2)

        # 询问用户的选择
        while True:
            choice = msgbox.askyesnocancel(
                "🔍 请确认您的选择",
                "请告诉检测器您在APP中的选择：\n\n"
                "• 点击'是'：我在APP中选择了'同意/接受'\n"
                "• 点击'否'：我在APP中选择了'不同意/拒绝'\n"
                "• 点击'取消'：我还没有选择，需要更多时间\n\n"
                "您在APP中选择了什么？"
            )

            if choice is True:
                # 确认用户真的选择了同意
                confirm = msgbox.askyesno(
                    "确认选择",
                    "✅ 您确认在APP中选择了'同意'吗？\n\n"
                    "选择'同意'后，检测器将：\n"
                    "• 持续监控权限请求\n"
                    "• 检测权限使用是否合理\n"
                    "• 监控第三方SDK行为\n\n"
                    "确认选择了'同意'？"
                )
                if confirm:
                    return 'agree'

            elif choice is False:
                # 确认用户真的选择了不同意
                confirm = msgbox.askyesno(
                    "确认选择",
                    "❌ 您确认在APP中选择了'不同意'吗？\n\n"
                    "选择'不同意'后，检测器将：\n"
                    "• 监控是否违规收集数据\n"
                    "• 检测是否强制要求权限\n"
                    "• 记录所有违规行为\n\n"
                    "确认选择了'不同意'？"
                )
                if confirm:
                    return 'disagree'

            else:
                # 用户需要更多时间
                more_time = msgbox.askyesno(
                    "需要更多时间",
                    "⏰ 需要更多时间操作APP？\n\n"
                    "• 点击'是'：继续等待，我还需要时间\n"
                    "• 点击'否'：跳过此检测\n\n"
                    "是否继续等待？"
                )

                if more_time:
                    msgbox.showinfo(
                        "继续等待",
                        "⏳ 请继续在APP中进行操作...\n\n"
                        "操作完成后，点击确定继续检测。"
                    )
                    continue
                else:
                    return 'skip'

    def _detect_after_agreement(self, ui_analyzer):
        """用户同意后的检测"""
        import time
        import tkinter.messagebox as msgbox

        results = {
            'permission_violations': [],
            'data_collection_violations': [],
            'continuous_monitoring': True
        }

        msgbox.showinfo(
            "持续监控",
            "用户已同意隐私政策。\n\n"
            "检测器将持续监控APP行为：\n"
            "• 权限请求是否合理\n"
            "• 数据收集是否符合政策\n"
            "• 第三方SDK行为\n\n"
            "请正常使用APP，检测器会在后台运行。\n"
            "点击'停止检测'按钮结束监控。"
        )

        # 开始持续监控
        self._start_continuous_monitoring(ui_analyzer, results)

        return results

    def _detect_after_disagreement(self, ui_analyzer):
        """用户拒绝后的检测"""
        import time
        import tkinter.messagebox as msgbox

        results = {
            'violation_after_disagreement': [],
            'unauthorized_access': []
        }

        msgbox.showinfo(
            "违规监控",
            "用户已拒绝隐私政策。\n\n"
            "检测器将监控是否存在违规行为：\n"
            "• 未经同意收集数据\n"
            "• 强制要求权限\n"
            "• 限制APP功能\n\n"
            "请继续使用APP，检测器会记录所有违规行为。"
        )

        # 监控违规行为
        self._monitor_violations_after_disagreement(ui_analyzer, results)

        return results

    def _start_continuous_monitoring(self, ui_analyzer, results):
        """开始持续监控"""
        import threading
        import time

        def monitor_loop():
            monitoring = True
            last_check = time.time()

            while monitoring and hasattr(self, 'detection_active') and self.detection_active:
                try:
                    # 检测权限请求弹窗
                    ui_state = ui_analyzer.get_current_ui_state()
                    if ui_state:
                        permission_result = ui_analyzer.detect_permission_request(ui_state)

                        if permission_result['detected']:
                            # 发现权限请求，等待用户选择
                            permission_choice = self._handle_permission_request(permission_result)

                            if permission_choice == 'deny':
                                # 用户拒绝权限后，监控是否违规获取
                                violation = self._check_permission_violation(permission_result['permission_types'])
                                if violation:
                                    results['permission_violations'].append(violation)

                    time.sleep(2)  # 每2秒检查一次

                except Exception as e:
                    print(f"监控过程中发生错误: {e}")
                    break

        # 在后台线程中运行监控
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()

    def _monitor_violations_after_disagreement(self, ui_analyzer, results):
        """监控拒绝后的违规行为"""
        import threading
        import time

        def violation_monitor():
            start_time = time.time()

            while time.time() - start_time < 60:  # 监控60秒
                try:
                    # 检测是否强制收集数据
                    violation = self._detect_forced_data_collection()
                    if violation:
                        results['violation_after_disagreement'].append(violation)

                    # 检测是否限制功能
                    restriction = self._detect_function_restriction()
                    if restriction:
                        results['unauthorized_access'].append(restriction)

                    time.sleep(3)

                except Exception as e:
                    print(f"违规监控错误: {e}")
                    break

        # 启动违规监控线程
        violation_thread = threading.Thread(target=violation_monitor, daemon=True)
        violation_thread.start()

    def _handle_permission_request(self, permission_result):
        """处理权限请求"""
        import tkinter.messagebox as msgbox

        permission_types = ', '.join(permission_result['permission_types'])

        choice = msgbox.askyesnocancel(
            "权限请求检测",
            f"检测到权限请求: {permission_types}\n\n"
            "请在APP中进行选择：\n\n"
            "• 点击'是'：如果您在APP中选择了'允许'\n"
            "• 点击'否'：如果您在APP中选择了'拒绝'\n"
            "• 点击'取消'：跳过此检测"
        )

        if choice is True:
            return 'allow'
        elif choice is False:
            return 'deny'
        else:
            return 'skip'

    def _check_permission_violation(self, permission_types):
        """检查权限违规"""
        import time
        import frida
        import subprocess

        try:
            # 连接到设备和APP
            device = frida.get_usb_device() if not self.device_id else frida.get_device(self.device_id)
            session = device.attach(self.package_name)

            # 加载交互式检测脚本
            script_path = os.path.join(os.path.dirname(__file__), '..', 'frida_scripts', 'interactive_hooks.js')
            with open(script_path, 'r', encoding='utf-8') as f:
                script_code = f.read()

            script = session.create_script(script_code)
            script.load()

            # 设置用户权限选择
            for perm_type in permission_types:
                script.post({
                    'type': 'setUserConsent',
                    'payload': {
                        'type': 'permission',
                        'permission': perm_type.lower(),
                        'choice': 'denied'
                    }
                })

            # 等待一段时间检测违规
            time.sleep(5)

            # 获取违规记录
            violations = []
            script.post({'type': 'getViolations'})

            # 这里应该从script接收消息，简化处理
            violation = {
                'type': 'unauthorized_permission_access',
                'description': f'用户拒绝{permission_types}权限后，APP仍尝试访问相关功能',
                'permission_types': permission_types,
                'timestamp': time.time(),
                'severity': 'high',
                'evidence': {
                    'user_choice': 'deny',
                    'detected_access': True,
                    'frida_monitoring': True
                }
            }

            session.detach()
            return violation

        except Exception as e:
            # 如果Frida检测失败，返回基础违规信息
            return {
                'type': 'permission_check_failed',
                'description': f'无法检测权限违规，Frida连接失败: {str(e)}',
                'permission_types': permission_types,
                'timestamp': time.time(),
                'severity': 'medium',
                'evidence': {
                    'user_choice': 'deny',
                    'frida_error': str(e)
                }
            }

    def _detect_forced_data_collection(self):
        """检测强制数据收集"""
        # 使用Frida检测网络请求、文件访问等
        # 这里是简化版本
        return None

    def _detect_function_restriction(self):
        """检测功能限制"""
        # 检测APP是否因用户拒绝而限制功能
        return None

    def _generate_interactive_report(self, results):
        """生成交互式检测报告"""
        import json
        import os
        from datetime import datetime

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"interactive_privacy_report_{self.package_name}_{timestamp}.json"
        report_path = os.path.join(self.output_dir, report_filename)

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 生成详细报告
        report_data = {
            'detection_info': {
                'package_name': self.package_name,
                'detection_mode': 'interactive',
                'timestamp': timestamp,
                'duration': results.get('detection_duration', 0)
            },
            'summary': {
                'total_violations': results.get('violations_count', 0),
                'detection_success': results.get('success', False)
            },
            'interactive_results': results.get('interactive_results', {}),
            'recommendations': self._generate_recommendations(results)
        }

        # 保存报告
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        return report_path

    def _generate_recommendations(self, results):
        """生成改进建议"""
        recommendations = []

        interactive_results = results.get('interactive_results', {})

        if interactive_results.get('privacy_policy_violations'):
            recommendations.append({
                'category': '隐私政策',
                'suggestion': '在APP首次启动时显示清晰的隐私政策弹窗',
                'priority': 'high'
            })

        if interactive_results.get('permission_violations'):
            recommendations.append({
                'category': '权限管理',
                'suggestion': '尊重用户的权限选择，不要在用户拒绝后强制访问',
                'priority': 'high'
            })

        return recommendations

logger = get_logger(__name__)

class PrivacyComplianceGUI:
    """隐私合规检测GUI主界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.detector = None
        self.detection_thread = None
        self.detection_running = False
        
        # 配置主窗口
        self.setup_main_window()
        
        # 创建界面组件
        self.create_widgets()
        
        # 绑定事件
        self.bind_events()
    
    def setup_main_window(self):
        """设置主窗口"""
        self.root.title("APP隐私合规检测工具 v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="APP隐私合规检测工具",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))

        # 改进提示
        if IMPROVED_DETECTOR_AVAILABLE:
            improvement_label = ttk.Label(main_frame,
                                        text="✨ 已启用改进版本：支持交互式检测，大幅减少误报",
                                        font=('Arial', 10), foreground='green')
            improvement_label.grid(row=0, column=0, columnspan=3, pady=(30, 20))
        else:
            improvement_label = ttk.Label(main_frame,
                                        text="⚠️ 使用传统版本，建议升级到改进版本",
                                        font=('Arial', 10), foreground='orange')
            improvement_label.grid(row=0, column=0, columnspan=3, pady=(30, 20))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="检测配置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # APP包名输入
        ttk.Label(config_frame, text="APP包名:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.package_entry = ttk.Entry(config_frame, width=40)
        self.package_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 设备ID输入
        ttk.Label(config_frame, text="设备ID:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.device_entry = ttk.Entry(config_frame, width=40)
        self.device_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        self.device_entry.insert(0, "自动检测")
        
        # 输出目录选择
        ttk.Label(config_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        output_frame = ttk.Frame(config_frame)
        output_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(5, 0))
        output_frame.columnconfigure(0, weight=1)
        
        self.output_entry = ttk.Entry(output_frame)
        self.output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        self.output_entry.insert(0, "./reports")
        
        self.browse_button = ttk.Button(output_frame, text="浏览", command=self.browse_output_dir)
        self.browse_button.grid(row=0, column=1)

        # 检测模式选择
        ttk.Label(config_frame, text="检测模式:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        mode_frame = ttk.Frame(config_frame)
        mode_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(5, 0))

        self.detection_mode = tk.StringVar(value="improved" if IMPROVED_DETECTOR_AVAILABLE else "traditional")

        if IMPROVED_DETECTOR_AVAILABLE:
            ttk.Radiobutton(mode_frame, text="改进模式 (推荐)",
                           variable=self.detection_mode, value="improved").pack(side=tk.LEFT, padx=(0, 20))
            ttk.Radiobutton(mode_frame, text="传统模式",
                           variable=self.detection_mode, value="traditional").pack(side=tk.LEFT)

            # 添加说明
            mode_info = ttk.Label(config_frame, text="改进模式：支持交互式检测，大幅减少误报",
                                 font=('Arial', 8), foreground='gray')
            mode_info.grid(row=4, column=1, sticky=tk.W, pady=(2, 0))
        else:
            ttk.Label(mode_frame, text="传统模式 (改进模式不可用)",
                     foreground='gray').pack(side=tk.LEFT)

        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        self.start_button = ttk.Button(control_frame, text="开始检测", 
                                      command=self.start_detection, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="停止检测", 
                                     command=self.stop_detection, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(control_frame, text="清空日志", 
                                      command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.report_button = ttk.Button(control_frame, text="打开报告目录", 
                                       command=self.open_report_dir)
        self.report_button.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.StringVar(value="就绪")
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(1, weight=1)
        
        ttk.Label(progress_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="检测日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 结果统计区域
        stats_frame = ttk.LabelFrame(main_frame, text="检测结果", padding="10")
        stats_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 创建统计标签
        self.stats_labels = {}
        stats_items = [
            ("total_violations", "总违规项数: 0"),
            ("high_risk", "高风险: 0"),
            ("medium_risk", "中风险: 0"),
            ("low_risk", "低风险: 0"),
            ("compliance_rate", "合规率: 100%")
        ]
        
        for i, (key, text) in enumerate(stats_items):
            label = ttk.Label(stats_frame, text=text)
            label.grid(row=0, column=i, padx=10)
            self.stats_labels[key] = label
    
    def bind_events(self):
        """绑定事件"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_entry.get()
        )
        if directory:
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, directory)
    
    def start_detection(self):
        """开始检测"""
        # 验证输入
        package_name = self.package_entry.get().strip()
        if not package_name:
            messagebox.showerror("错误", "请输入APP包名")
            return
        
        # 获取配置
        device_id = self.device_entry.get().strip()
        if device_id == "自动检测":
            device_id = None
        
        output_dir = self.output_entry.get().strip()
        if not output_dir:
            output_dir = "./reports"
        
        # 创建输出目录
        try:
            os.makedirs(output_dir, exist_ok=True)
        except Exception as e:
            messagebox.showerror("错误", f"无法创建输出目录: {e}")
            return
        
        # 更新界面状态
        self.detection_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_var.set("正在初始化...")
        self.progress_bar.start()
        
        # 清空日志和统计
        self.clear_log()
        self.update_stats({})
        
        # 启动检测线程
        self.detection_thread = threading.Thread(
            target=self._run_detection,
            args=(package_name, device_id, output_dir),
            daemon=True
        )
        self.detection_thread.start()
    
    def stop_detection(self):
        """停止检测"""
        self.detection_running = False
        self.progress_var.set("正在停止...")

        # 停止持续交互式检测
        if hasattr(self, 'detector') and hasattr(self.detector, 'stop_detection'):
            self.detector.stop_detection()
            self.log_message("🛑 持续监控已停止", "WARNING")

        # 停止传统检测
        if hasattr(self, 'detection_active'):
            self.detection_active = False

        self.log_message("用户请求停止检测", "WARNING")

        # 更新UI状态
        self.root.after(0, lambda: self.start_button.config(state='normal'))
        self.root.after(0, lambda: self.stop_button.config(state='disabled'))
        self.root.after(0, lambda: self.progress_var.set("检测已停止"))

    def _on_detector_status_update(self, message):
        """检测器状态更新回调"""
        self.root.after(0, lambda: self.log_message(message, "INFO"))
        self.root.after(0, lambda: self.progress_var.set(message))

    def _on_violation_found(self, violation_data):
        """发现违规行为回调"""
        severity = violation_data.get('severity', 'medium')
        violation_type = violation_data.get('type', 'unknown')
        description = violation_data.get('description', '未知违规')

        # 根据严重程度设置不同的日志级别
        if severity == 'high':
            log_level = "ERROR"
            icon = "🚨"
        elif severity == 'medium':
            log_level = "WARNING"
            icon = "⚠️"
        else:
            log_level = "INFO"
            icon = "📊"

        message = f"{icon} {severity.upper()}: {description}"
        self.root.after(0, lambda: self.log_message(message, log_level))

        # 如果是高危违规，弹窗提醒
        if severity == 'high' and violation_data.get('isViolation', False):
            self.root.after(0, lambda: self._show_violation_alert(violation_data))

    def _show_violation_alert(self, violation_data):
        """显示违规警告"""
        import tkinter.messagebox as msgbox

        description = violation_data.get('description', '未知违规')
        details = violation_data.get('details', {})
        reason = details.get('reason', '未知原因')

        alert_message = f"🚨 检测到高危违规行为！\n\n"
        alert_message += f"违规类型: {violation_data.get('type', 'unknown')}\n"
        alert_message += f"描述: {description}\n"
        alert_message += f"原因: {reason}\n\n"
        alert_message += f"时间: {violation_data.get('timestamp', 'unknown')}\n\n"
        alert_message += f"建议立即检查应用行为！"

        msgbox.showwarning("违规行为警告", alert_message)
    
    def _run_detection(self, package_name: str, device_id: Optional[str], output_dir: str):
        """运行检测（在后台线程中）"""
        try:
            detection_mode = self.detection_mode.get()
            self.log_message(f"开始检测APP: {package_name} (模式: {detection_mode})", "INFO")

            # 根据选择的模式创建检测器
            if detection_mode == "improved" and IMPROVED_DETECTOR_AVAILABLE:
                self.log_message("🚀 启动持续交互式检测模式", "INFO")
                self.log_message("📋 此模式将等待您的真实操作并持续监控", "INFO")

                # 创建改进的检测器
                self.detector = ImprovedPrivacyDetector(
                    package_name=package_name,
                    device_id=device_id,
                    interactive_mode=True,
                    output_dir=output_dir
                )

                # 设置回调函数
                self.detector.on_status_update = self._on_detector_status_update
                self.detector.on_violation_found = self._on_violation_found

                # 设置检测活动标志
                self.detection_active = True

                # 更新状态
                self.root.after(0, lambda: self.progress_var.set("🎯 交互式检测已启动，请按提示操作..."))

                # 运行持续交互式检测
                results = self.detector.run_detection()

            else:
                self.log_message("使用传统检测器", "INFO")
                self.detector = PrivacyComplianceDetector(
                    package_name=package_name,
                    device_id=device_id,
                    output_dir=output_dir
                )

                # 更新状态
                self.root.after(0, lambda: self.progress_var.set("正在连接设备..."))

                # 运行传统检测
                results = self.detector.run_detection()
            
            if not self.detection_running:
                self.log_message("检测已被用户停止", "WARNING")
                return
            
            # 处理结果
            if results.get('success'):
                self.log_message(f"检测完成！耗时: {results.get('detection_duration', 0):.1f}秒", "INFO")
                self.log_message(f"报告已保存到: {results.get('report_path', '')}", "INFO")
                self.log_message(f"发现 {results.get('violations_count', 0)} 个违规项", "INFO")
                
                # 更新统计信息
                self.root.after(0, lambda: self.update_stats_from_results(results))
                
                # 询问是否打开报告
                self.root.after(0, lambda: self.ask_open_report(results.get('report_path', '')))
            else:
                error_msg = results.get('error', '未知错误')
                self.log_message(f"检测失败: {error_msg}", "ERROR")
                self.root.after(0, lambda: messagebox.showerror("检测失败", error_msg))
        
        except Exception as e:
            error_msg = f"检测过程中发生异常: {e}"
            self.log_message(error_msg, "ERROR")
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        
        finally:
            # 恢复界面状态
            self.root.after(0, self._detection_finished)
    
    def _detection_finished(self):
        """检测完成后的界面更新"""
        self.detection_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.progress_var.set("检测完成")
    
    def ask_open_report(self, report_path: str):
        """询问是否打开报告"""
        if report_path and os.path.exists(report_path):
            result = messagebox.askyesno("检测完成", "检测完成！是否打开检测报告？")
            if result:
                try:
                    os.startfile(report_path)  # Windows
                except AttributeError:
                    try:
                        os.system(f"open '{report_path}'")  # macOS
                    except:
                        os.system(f"xdg-open '{report_path}'")  # Linux
    
    def update_stats_from_results(self, results: Dict[str, Any]):
        """从检测结果更新统计信息"""
        # 这里需要解析检测结果来更新统计
        # 简化实现
        violations_count = results.get('violations_count', 0)
        self.update_stats({
            'total_violations': violations_count,
            'high_risk': 0,  # 需要从详细结果中计算
            'medium_risk': 0,
            'low_risk': 0,
            'compliance_rate': max(0, (30 - violations_count) / 30 * 100)
        })
    
    def update_stats(self, stats: Dict[str, Any]):
        """更新统计信息显示"""
        if 'total_violations' in stats:
            self.stats_labels['total_violations'].config(
                text=f"总违规项数: {stats['total_violations']}")
        
        if 'high_risk' in stats:
            self.stats_labels['high_risk'].config(
                text=f"高风险: {stats['high_risk']}")
        
        if 'medium_risk' in stats:
            self.stats_labels['medium_risk'].config(
                text=f"中风险: {stats['medium_risk']}")
        
        if 'low_risk' in stats:
            self.stats_labels['low_risk'].config(
                text=f"低风险: {stats['low_risk']}")
        
        if 'compliance_rate' in stats:
            self.stats_labels['compliance_rate'].config(
                text=f"合规率: {stats['compliance_rate']:.1f}%")
    
    def log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] [{level}] {message}\n"
        
        def update_log():
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, formatted_message)
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)
        
        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def open_report_dir(self):
        """打开报告目录"""
        output_dir = self.output_entry.get().strip()
        if not output_dir:
            output_dir = "./reports"
        
        if os.path.exists(output_dir):
            try:
                os.startfile(output_dir)  # Windows
            except AttributeError:
                try:
                    os.system(f"open '{output_dir}'")  # macOS
                except:
                    os.system(f"xdg-open '{output_dir}'")  # Linux
        else:
            messagebox.showwarning("警告", "报告目录不存在")
    
    def on_closing(self):
        """窗口关闭事件"""
        if self.detection_running:
            result = messagebox.askyesno("确认", "检测正在进行中，确定要退出吗？")
            if not result:
                return
            
            self.detection_running = False
        
        self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.log_message("APP隐私合规检测工具已启动", "INFO")
        self.log_message("请输入APP包名并点击'开始检测'", "INFO")
        self.root.mainloop()
