#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的隐私合规检测器
支持交互式检测，大幅减少误报
"""

import time
import json
import argparse
import sys
import os
from typing import Dict, Any, Optional

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.dirname(current_dir))

from core.detector import PrivacyComplianceDetector
from core.interactive_detector import InteractivePrivacyDetector
from utils.logger import get_logger
from utils.ui_analyzer import UIAnalyzer

logger = get_logger(__name__)

class ImprovedPrivacyDetector:
    """改进的隐私合规检测器"""
    
    def __init__(self, package_name: str, device_id: Optional[str] = None,
                 interactive_mode: bool = True, output_dir: str = "./reports"):
        self.package_name = package_name
        self.device_id = device_id
        self.interactive_mode = interactive_mode
        self.output_dir = output_dir
        
        # 初始化组件
        self.ui_analyzer = UIAnalyzer(device_id)
        self.main_detector = None
        self.interactive_detector = None
        
        # 检测结果
        self.detection_results = {
            'success': False,
            'interactive_results': {},
            'traditional_results': {},
            'merged_results': {},
            'detection_summary': {}
        }
    
    def run_detection(self) -> Dict[str, Any]:
        """运行改进的检测流程"""
        logger.info(f"开始改进的隐私合规检测: {self.package_name}")
        logger.info(f"交互模式: {'启用' if self.interactive_mode else '禁用'}")
        
        try:
            if self.interactive_mode:
                # 交互式检测模式
                results = self._run_interactive_detection()
            else:
                # 传统检测模式
                results = self._run_traditional_detection()
            
            # 生成检测摘要
            self._generate_detection_summary(results)
            
            return results
            
        except Exception as e:
            logger.error(f"检测过程中发生错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'package_name': self.package_name
            }
    
    def _run_interactive_detection(self) -> Dict[str, Any]:
        """运行交互式检测"""
        logger.info("启动交互式检测模式")
        
        # 初始化主检测器（用于Frida连接）
        self.main_detector = PrivacyComplianceDetector(
            self.package_name, self.device_id, self.output_dir
        )
        
        # 连接设备和APP
        self.main_detector._connect_to_device()
        self.main_detector._attach_to_app()
        self.main_detector._load_frida_scripts()
        
        # 初始化交互式检测器
        self.interactive_detector = InteractivePrivacyDetector(
            self.main_detector.session,
            self.main_detector.device,
            self.main_detector.config,
            self.ui_analyzer
        )
        
        # 设置回调函数
        self.interactive_detector.on_violation_found = self._on_violation_found
        self.interactive_detector.on_user_action_needed = self._on_user_action_needed
        self.interactive_detector.on_progress_update = self._on_progress_update
        
        # 开始交互式检测
        interactive_results = self.interactive_detector.start_interactive_detection()
        
        # 清理资源
        self.main_detector._cleanup()
        
        return {
            'success': True,
            'detection_mode': 'interactive',
            'package_name': self.package_name,
            'interactive_results': interactive_results,
            'detection_duration': time.time() - self.main_detector.start_time if self.main_detector.start_time else 0
        }
    
    def _run_traditional_detection(self) -> Dict[str, Any]:
        """运行传统检测模式"""
        logger.info("启动传统检测模式")
        
        self.main_detector = PrivacyComplianceDetector(
            self.package_name, self.device_id, self.output_dir
        )
        
        return self.main_detector.run_detection()
    
    def _on_violation_found(self, violation_data):
        """处理发现的违规"""
        violation_type = violation_data.get('type', 'unknown')
        severity = violation_data.get('severity', 'medium')
        description = violation_data.get('description', '')
        
        print(f"\n⚠️  发现违规: {violation_type}")
        print(f"   严重程度: {severity}")
        print(f"   描述: {description}")
        
        if violation_data.get('recommendation'):
            print(f"   建议: {violation_data['recommendation']}")
        
        print("-" * 60)
    
    def _on_user_action_needed(self, action_info):
        """处理需要用户操作的情况"""
        action_type = action_info.get('action_type')
        message = action_info.get('message', '')
        
        print(f"\n{'='*60}")
        print(f"🔔 需要用户操作: {action_type}")
        print(f"📝 说明: {message}")
        print(f"{'='*60}")
        
        if action_type == 'privacy_policy_choice':
            print("请在APP中选择:")
            print("1. 点击'同意'按钮")
            print("2. 点击'不同意'按钮")
            print("3. 输入'skip'跳过此检测")
            
            while True:
                user_input = input("\n请输入您的选择 (agree/disagree/skip): ").strip().lower()
                if user_input in ['agree', '同意', '1', 'disagree', '不同意', '2', 'skip']:
                    return user_input
                print("无效输入，请重新输入")
                
        elif action_type == 'permission_request':
            print("检测到权限请求弹窗，请选择:")
            print("1. 点击'允许'")
            print("2. 点击'拒绝'")
            print("3. 输入'skip'跳过此检测")
            
            while True:
                user_input = input("\n请输入您的选择 (allow/deny/skip): ").strip().lower()
                if user_input in ['allow', '允许', '1', 'deny', '拒绝', '2', 'skip']:
                    return user_input
                print("无效输入，请重新输入")
        
        return 'skip'
    
    def _on_progress_update(self, progress_info):
        """处理进度更新"""
        stage = progress_info.get('stage', '')
        progress = progress_info.get('progress', 0)
        message = progress_info.get('message', '')
        
        print(f"[{stage}] {progress}% - {message}")
    
    def _generate_detection_summary(self, results):
        """生成检测摘要"""
        if not results.get('success'):
            return
        
        summary = {
            'total_violations': 0,
            'violations_by_category': {},
            'violations_by_severity': {'high': 0, 'medium': 0, 'low': 0},
            'detection_highlights': []
        }
        
        # 统计违规数量
        if 'interactive_results' in results:
            interactive_results = results['interactive_results']
            for category, violations in interactive_results.items():
                if isinstance(violations, list):
                    summary['total_violations'] += len(violations)
                    summary['violations_by_category'][category] = len(violations)
                    
                    for violation in violations:
                        severity = violation.get('severity', 'medium')
                        if severity in summary['violations_by_severity']:
                            summary['violations_by_severity'][severity] += 1
        
        # 生成检测亮点
        if summary['total_violations'] == 0:
            summary['detection_highlights'].append("✅ 未发现明显的隐私合规违规")
        else:
            if summary['violations_by_severity']['high'] > 0:
                summary['detection_highlights'].append(f"🚨 发现 {summary['violations_by_severity']['high']} 个高风险违规")
            if summary['violations_by_severity']['medium'] > 0:
                summary['detection_highlights'].append(f"⚠️ 发现 {summary['violations_by_severity']['medium']} 个中风险违规")
        
        results['detection_summary'] = summary
        
        # 打印摘要
        print(f"\n{'='*60}")
        print("🔍 检测摘要")
        print(f"{'='*60}")
        print(f"📱 APP包名: {self.package_name}")
        print(f"🔢 总违规数: {summary['total_violations']}")
        print(f"🔴 高风险: {summary['violations_by_severity']['high']}")
        print(f"🟡 中风险: {summary['violations_by_severity']['medium']}")
        print(f"🟢 低风险: {summary['violations_by_severity']['low']}")
        
        if summary['detection_highlights']:
            print("\n📋 检测亮点:")
            for highlight in summary['detection_highlights']:
                print(f"   {highlight}")
        
        print(f"{'='*60}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='改进的隐私合规检测工具')
    parser.add_argument('package_name', help='目标APP包名')
    parser.add_argument('-d', '--device', help='设备ID')
    parser.add_argument('-o', '--output', default='./reports', help='输出目录')
    parser.add_argument('--no-interactive', action='store_true', help='禁用交互模式')
    
    args = parser.parse_args()
    
    # 创建检测器
    detector = ImprovedPrivacyDetector(
        package_name=args.package_name,
        device_id=args.device,
        interactive_mode=not args.no_interactive,
        output_dir=args.output
    )
    
    # 运行检测
    print(f"🚀 开始检测APP: {args.package_name}")
    print(f"📱 设备: {args.device or '自动选择'}")
    print(f"🔧 模式: {'交互式' if not args.no_interactive else '传统'}")
    print("-" * 60)
    
    results = detector.run_detection()
    
    if results.get('success'):
        print("\n✅ 检测完成！")
        if 'report_path' in results:
            print(f"📄 报告已生成: {results['report_path']}")
    else:
        print(f"\n❌ 检测失败: {results.get('error', '未知错误')}")

if __name__ == '__main__':
    main()
