/**
 * UI相关的隐私合规检测脚本
 * 监控隐私政策显示、用户交互、弹窗等UI行为
 */

var uiEvents = [];
var dialogEvents = [];
var privacyPolicyEvents = [];

function log(level, message, data) {
    var timestamp = Date.now();
    send({
        type: 'ui_log',
        payload: {
            timestamp: timestamp,
            level: level,
            message: message,
            data: data || {}
        }
    });
}

function addUIViolation(type, description, evidence, severity) {
    var violation = {
        type: type,
        description: description,
        evidence: evidence,
        severity: severity || 'medium',
        timestamp: Date.now(),
        category: 'ui'
    };
    
    send({
        type: 'ui_violation',
        payload: violation
    });
}

// 1. 监控Dialog显示
function hookDialogs() {
    try {
        var AlertDialog = Java.use("android.app.AlertDialog");
        var Dialog = Java.use("android.app.Dialog");
        
        // Hook AlertDialog.show()
        AlertDialog.show.implementation = function() {
            var result = this.show();
            
            try {
                var title = "";
                var message = "";
                
                // 尝试获取标题和内容
                try {
                    var titleView = this.findViewById(16908310); // android.R.id.title
                    if (titleView) {
                        title = titleView.getText().toString();
                    }
                } catch (e) {}
                
                try {
                    var messageView = this.findViewById(102); // android.R.id.message
                    if (messageView) {
                        message = messageView.getText().toString();
                    }
                } catch (e) {}
                
                var dialogEvent = {
                    type: 'alert_dialog',
                    title: title,
                    message: message,
                    timestamp: Date.now()
                };
                
                dialogEvents.push(dialogEvent);
                
                // 检查是否为隐私政策相关对话框
                var content = (title + " " + message).toLowerCase();
                if (content.includes('隐私') || content.includes('privacy') || 
                    content.includes('政策') || content.includes('policy') ||
                    content.includes('条款') || content.includes('terms')) {
                    
                    privacyPolicyEvents.push({
                        type: 'privacy_dialog',
                        title: title,
                        message: message,
                        timestamp: Date.now()
                    });
                    
                    log('info', 'Privacy policy dialog detected', dialogEvent);
                }
                
                log('debug', 'Dialog shown', dialogEvent);
                
            } catch (e) {
                log('error', 'Error processing dialog', {error: e.toString()});
            }
            
            return result;
        };
        
    } catch (e) {
        log('error', 'Failed to hook dialogs', {error: e.toString()});
    }
}

// 2. 监控Activity启动
function hookActivities() {
    try {
        var Activity = Java.use("android.app.Activity");
        
        // Hook onCreate
        Activity.onCreate.overload('android.os.Bundle').implementation = function(savedInstanceState) {
            var className = this.getClass().getName();
            
            var activityEvent = {
                type: 'activity_created',
                className: className,
                timestamp: Date.now()
            };
            
            uiEvents.push(activityEvent);
            
            // 检查是否为隐私政策相关Activity
            var classNameLower = className.toLowerCase();
            if (classNameLower.includes('privacy') || classNameLower.includes('policy') ||
                classNameLower.includes('terms') || classNameLower.includes('agreement')) {
                
                privacyPolicyEvents.push({
                    type: 'privacy_activity',
                    className: className,
                    timestamp: Date.now()
                });
                
                log('info', 'Privacy policy activity detected', activityEvent);
            }
            
            log('debug', 'Activity created', activityEvent);
            
            return this.onCreate(savedInstanceState);
        };
        
        // Hook onResume
        Activity.onResume.implementation = function() {
            var className = this.getClass().getName();
            
            var activityEvent = {
                type: 'activity_resumed',
                className: className,
                timestamp: Date.now()
            };
            
            uiEvents.push(activityEvent);
            log('debug', 'Activity resumed', activityEvent);
            
            return this.onResume();
        };
        
    } catch (e) {
        log('error', 'Failed to hook activities', {error: e.toString()});
    }
}

// 3. 监控WebView加载
function hookWebView() {
    try {
        var WebView = Java.use("android.webkit.WebView");
        
        // Hook loadUrl
        WebView.loadUrl.overload('java.lang.String').implementation = function(url) {
            var webViewEvent = {
                type: 'webview_load',
                url: url,
                timestamp: Date.now()
            };
            
            uiEvents.push(webViewEvent);
            
            // 检查是否加载隐私政策页面
            var urlLower = url.toLowerCase();
            if (urlLower.includes('privacy') || urlLower.includes('policy') ||
                urlLower.includes('terms') || urlLower.includes('agreement')) {
                
                privacyPolicyEvents.push({
                    type: 'privacy_webview',
                    url: url,
                    timestamp: Date.now()
                });
                
                log('info', 'Privacy policy WebView detected', webViewEvent);
            }
            
            log('debug', 'WebView loading URL', webViewEvent);
            
            return this.loadUrl(url);
        };
        
    } catch (e) {
        log('error', 'Failed to hook WebView', {error: e.toString()});
    }
}

// 4. 监控按钮点击
function hookButtons() {
    try {
        var View = Java.use("android.view.View");
        
        // Hook setOnClickListener
        View.setOnClickListener.implementation = function(listener) {
            var originalListener = listener;
            
            if (listener) {
                var OnClickListener = Java.use("android.view.View$OnClickListener");
                var proxyListener = Java.registerClass({
                    name: "com.privacy.ProxyClickListener",
                    implements: [OnClickListener],
                    methods: {
                        onClick: function(view) {
                            try {
                                // 尝试获取按钮文本
                                var buttonText = "";
                                try {
                                    if (view.getText) {
                                        buttonText = view.getText().toString();
                                    }
                                } catch (e) {}
                                
                                var clickEvent = {
                                    type: 'button_click',
                                    text: buttonText,
                                    viewClass: view.getClass().getName(),
                                    timestamp: Date.now()
                                };
                                
                                uiEvents.push(clickEvent);
                                
                                // 检查是否为隐私政策相关按钮
                                var textLower = buttonText.toLowerCase();
                                if (textLower.includes('同意') || textLower.includes('agree') ||
                                    textLower.includes('拒绝') || textLower.includes('deny') ||
                                    textLower.includes('接受') || textLower.includes('accept')) {
                                    
                                    privacyPolicyEvents.push({
                                        type: 'privacy_button_click',
                                        text: buttonText,
                                        action: textLower.includes('同意') || textLower.includes('agree') || 
                                               textLower.includes('接受') || textLower.includes('accept') ? 'accept' : 'deny',
                                        timestamp: Date.now()
                                    });
                                    
                                    log('info', 'Privacy policy button clicked', clickEvent);
                                }
                                
                                log('debug', 'Button clicked', clickEvent);
                                
                            } catch (e) {
                                log('error', 'Error in click listener', {error: e.toString()});
                            }
                            
                            // 调用原始监听器
                            originalListener.onClick(view);
                        }
                    }
                });
                
                listener = proxyListener.$new();
            }
            
            return this.setOnClickListener(listener);
        };
        
    } catch (e) {
        log('error', 'Failed to hook buttons', {error: e.toString()});
    }
}

// 初始化UI Hook
Java.perform(function() {
    log('info', 'UI privacy compliance detection started');
    
    hookDialogs();
    hookActivities();
    hookWebView();
    hookButtons();
    
    log('info', 'All UI hooks initialized successfully');
});

// 导出函数
rpc.exports = {
    getUIEvents: function() {
        return uiEvents;
    },
    
    getDialogEvents: function() {
        return dialogEvents;
    },
    
    getPrivacyPolicyEvents: function() {
        return privacyPolicyEvents;
    },
    
    resetUIEvents: function() {
        uiEvents = [];
        dialogEvents = [];
        privacyPolicyEvents = [];
        log('info', 'UI events reset');
    }
};
