/**
 * 第三方SDK隐私合规检测脚本
 * 监控第三方SDK的数据收集和传输行为
 */

var sdkActivities = [];
var thirdPartyTransfers = [];
var knownSDKs = [
    'com.google.android.gms',
    'com.facebook.ads',
    'com.tencent.mm',
    'com.alibaba',
    'com.baidu',
    'com.umeng',
    'com.sina.weibo',
    'com.bytedance',
    'com.xiaomi',
    'com.huawei'
];

function log(level, message, data) {
    send({
        type: 'sdk_log',
        payload: {
            timestamp: Date.now(),
            level: level,
            message: message,
            data: data || {}
        }
    });
}

function addSDKViolation(type, description, evidence, severity) {
    var violation = {
        type: type,
        description: description,
        evidence: evidence,
        severity: severity || 'medium',
        timestamp: Date.now(),
        category: 'sdk'
    };
    
    send({
        type: 'sdk_violation',
        payload: violation
    });
}

function identifySDK(className) {
    for (var i = 0; i < knownSDKs.length; i++) {
        if (className.startsWith(knownSDKs[i])) {
            return knownSDKs[i];
        }
    }
    return null;
}

// 1. 监控第三方SDK网络请求
function hookSDKNetwork() {
    try {
        // Hook HttpURLConnection
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        
        HttpURLConnection.getOutputStream.implementation = function() {
            var url = this.getURL().toString();
            var stackTrace = getStackTrace();
            
            // 检查调用栈中是否包含第三方SDK
            var sdkName = null;
            for (var i = 0; i < stackTrace.length; i++) {
                var frame = stackTrace[i];
                if (frame && frame.className) {
                    sdkName = identifySDK(frame.className);
                    if (sdkName) break;
                }
            }
            
            if (sdkName) {
                var transfer = {
                    type: 'sdk_network_request',
                    sdk: sdkName,
                    url: url,
                    method: this.getRequestMethod(),
                    timestamp: Date.now(),
                    stackTrace: stackTrace
                };
                
                thirdPartyTransfers.push(transfer);
                
                // 检查是否为未授权的数据传输
                if (url.includes('analytics') || url.includes('tracking') || 
                    url.includes('collect') || url.includes('report')) {
                    
                    addSDKViolation(
                        'unauthorized_sdk_transfer',
                        '第三方SDK未经授权进行数据传输',
                        transfer,
                        'high'
                    );
                }
                
                log('warning', 'Third-party SDK network request detected', transfer);
            }
            
            return this.getOutputStream();
        };
        
    } catch (e) {
        log('error', 'Failed to hook SDK network', {error: e.toString()});
    }
}

// 2. 监控第三方SDK数据收集
function hookSDKDataCollection() {
    try {
        // Hook SharedPreferences (常用于存储用户数据)
        var SharedPreferences = Java.use("android.content.SharedPreferences");
        var Editor = Java.use("android.content.SharedPreferences$Editor");
        
        Editor.putString.implementation = function(key, value) {
            var stackTrace = getStackTrace();
            var sdkName = null;
            
            for (var i = 0; i < stackTrace.length; i++) {
                var frame = stackTrace[i];
                if (frame && frame.className) {
                    sdkName = identifySDK(frame.className);
                    if (sdkName) break;
                }
            }
            
            if (sdkName) {
                var collection = {
                    type: 'sdk_data_storage',
                    sdk: sdkName,
                    key: key,
                    valueType: 'string',
                    timestamp: Date.now(),
                    stackTrace: stackTrace
                };
                
                sdkActivities.push(collection);
                
                // 检查是否存储敏感数据
                var keyLower = key.toLowerCase();
                if (keyLower.includes('imei') || keyLower.includes('phone') ||
                    keyLower.includes('location') || keyLower.includes('contact')) {
                    
                    addSDKViolation(
                        'sdk_sensitive_data_storage',
                        '第三方SDK存储敏感数据',
                        collection,
                        'high'
                    );
                }
                
                log('info', 'SDK data storage detected', collection);
            }
            
            return this.putString(key, value);
        };
        
    } catch (e) {
        log('error', 'Failed to hook SDK data collection', {error: e.toString()});
    }
}

// 3. 监控第三方SDK初始化
function hookSDKInitialization() {
    try {
        // 通用的初始化方法监控
        var commonInitMethods = ['init', 'initialize', 'start', 'setup'];
        
        for (var i = 0; i < knownSDKs.length; i++) {
            var sdkPackage = knownSDKs[i];
            
            try {
                // 尝试Hook常见的初始化类
                var possibleClasses = [
                    sdkPackage + '.SDK',
                    sdkPackage + '.Manager',
                    sdkPackage + '.Client',
                    sdkPackage + '.Analytics'
                ];
                
                for (var j = 0; j < possibleClasses.length; j++) {
                    try {
                        var clazz = Java.use(possibleClasses[j]);
                        
                        for (var k = 0; k < commonInitMethods.length; k++) {
                            var methodName = commonInitMethods[k];
                            
                            if (clazz[methodName]) {
                                (function(sdk, className, method) {
                                    clazz[method].implementation = function() {
                                        var initEvent = {
                                            type: 'sdk_initialization',
                                            sdk: sdk,
                                            className: className,
                                            method: method,
                                            timestamp: Date.now()
                                        };
                                        
                                        sdkActivities.push(initEvent);
                                        log('info', 'SDK initialization detected', initEvent);
                                        
                                        return this[method].apply(this, arguments);
                                    };
                                })(sdkPackage, possibleClasses[j], methodName);
                            }
                        }
                        
                    } catch (e) {
                        // 类不存在，继续尝试下一个
                    }
                }
                
            } catch (e) {
                log('debug', 'SDK not found or failed to hook', {sdk: sdkPackage, error: e.toString()});
            }
        }
        
    } catch (e) {
        log('error', 'Failed to hook SDK initialization', {error: e.toString()});
    }
}

// 4. 监控广告SDK行为
function hookAdSDKs() {
    try {
        // Google AdMob
        try {
            var AdRequest = Java.use("com.google.android.gms.ads.AdRequest");
            var AdView = Java.use("com.google.android.gms.ads.AdView");
            
            AdView.loadAd.implementation = function(adRequest) {
                var adEvent = {
                    type: 'ad_request',
                    sdk: 'com.google.android.gms',
                    adType: 'banner',
                    timestamp: Date.now()
                };
                
                sdkActivities.push(adEvent);
                log('info', 'Ad request detected', adEvent);
                
                return this.loadAd(adRequest);
            };
            
        } catch (e) {
            // AdMob not present
        }
        
        // Facebook Ads
        try {
            var AdView = Java.use("com.facebook.ads.AdView");
            
            AdView.loadAd.implementation = function() {
                var adEvent = {
                    type: 'ad_request',
                    sdk: 'com.facebook.ads',
                    adType: 'banner',
                    timestamp: Date.now()
                };
                
                sdkActivities.push(adEvent);
                log('info', 'Facebook ad request detected', adEvent);
                
                return this.loadAd();
            };
            
        } catch (e) {
            // Facebook Ads not present
        }
        
    } catch (e) {
        log('error', 'Failed to hook ad SDKs', {error: e.toString()});
    }
}

function getStackTrace() {
    try {
        var trace = Thread.backtrace(this.context, Backtracer.ACCURATE);
        return trace.map(function(addr) {
            var symbol = DebugSymbol.fromAddress(addr);
            return {
                address: addr.toString(),
                className: symbol.moduleName || '',
                methodName: symbol.name || ''
            };
        }).slice(0, 10);
    } catch (e) {
        return [];
    }
}

// 初始化SDK Hook
Java.perform(function() {
    log('info', 'SDK privacy compliance detection started');
    
    hookSDKNetwork();
    hookSDKDataCollection();
    hookSDKInitialization();
    hookAdSDKs();
    
    log('info', 'All SDK hooks initialized successfully');
});

// 导出函数
rpc.exports = {
    getSDKActivities: function() {
        return sdkActivities;
    },
    
    getThirdPartyTransfers: function() {
        return thirdPartyTransfers;
    },
    
    resetSDKData: function() {
        sdkActivities = [];
        thirdPartyTransfers = [];
        log('info', 'SDK data reset');
    }
};
