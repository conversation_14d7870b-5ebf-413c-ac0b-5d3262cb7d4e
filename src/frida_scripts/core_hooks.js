/**
 * APP隐私合规检测 - 核心Hook脚本
 * 监控权限申请、数据收集、网络传输等关键行为
 */

// 全局变量
var violations = [];
var startTime = Date.now();
var packageName = "";
var privacyPolicyShown = false;
var userConsented = false;
var permissionRequests = [];
var dataCollections = [];
var networkRequests = [];

// 工具函数
function log(level, message, data) {
    var timestamp = Date.now() - startTime;
    var logEntry = {
        timestamp: timestamp,
        level: level,
        message: message,
        data: data || {},
        stackTrace: getStackTrace()
    };
    
    send({
        type: 'log',
        payload: logEntry
    });
}

function getStackTrace() {
    try {
        var trace = Thread.backtrace(this.context, Backtracer.ACCURATE);
        return trace.map(DebugSymbol.fromAddress).slice(0, 10);
    } catch (e) {
        return [];
    }
}

function addViolation(type, description, evidence, severity) {
    var violation = {
        id: violations.length + 1,
        type: type,
        description: description,
        evidence: evidence,
        severity: severity || 'medium',
        timestamp: Date.now() - startTime,
        stackTrace: getStackTrace()
    };
    
    violations.push(violation);
    
    send({
        type: 'violation',
        payload: violation
    });
    
    log('warning', 'Privacy violation detected', violation);
}

// 1. 监控权限申请
function hookPermissions() {
    try {
        // Hook ActivityCompat.requestPermissions
        var ActivityCompat = Java.use("androidx.core.app.ActivityCompat");
        ActivityCompat.requestPermissions.overload(
            'android.app.Activity', 
            '[Ljava.lang.String;', 
            'int'
        ).implementation = function(activity, permissions, requestCode) {
            
            var permissionArray = [];
            for (var i = 0; i < permissions.length; i++) {
                permissionArray.push(permissions[i]);
            }
            
            var request = {
                permissions: permissionArray,
                requestCode: requestCode,
                timestamp: Date.now() - startTime,
                beforeConsent: !userConsented
            };
            
            permissionRequests.push(request);
            
            // 检查是否在用户同意前申请权限 (违规项目9)
            if (!userConsented) {
                addViolation(
                    'premature_permission_request',
                    '在用户同意隐私政策前申请权限',
                    {
                        permissions: permissionArray,
                        requestCode: requestCode
                    },
                    'high'
                );
            }
            
            log('info', 'Permission request detected', request);
            
            return this.requestPermissions(activity, permissions, requestCode);
        };
        
        // Hook 系统权限检查
        var ContextCompat = Java.use("androidx.core.content.ContextCompat");
        ContextCompat.checkSelfPermission.implementation = function(context, permission) {
            var result = this.checkSelfPermission(context, permission);
            
            log('debug', 'Permission check', {
                permission: permission,
                granted: result === 0
            });
            
            return result;
        };
        
    } catch (e) {
        log('error', 'Failed to hook permissions', {error: e.toString()});
    }
}

// 2. 监控设备信息收集
function hookDeviceInfo() {
    try {
        // Hook TelephonyManager
        var TelephonyManager = Java.use("android.telephony.TelephonyManager");
        
        // IMEI/设备ID
        if (TelephonyManager.getDeviceId) {
            TelephonyManager.getDeviceId.overload().implementation = function() {
                var result = this.getDeviceId();
                
                var collection = {
                    type: 'device_id',
                    value: result,
                    method: 'TelephonyManager.getDeviceId',
                    timestamp: Date.now() - startTime,
                    beforeConsent: !userConsented
                };
                
                dataCollections.push(collection);
                
                if (!userConsented) {
                    addViolation(
                        'premature_data_collection',
                        '在用户同意前收集设备ID',
                        collection,
                        'high'
                    );
                }
                
                log('warning', 'Device ID collected', collection);
                return result;
            };
        }
        
        // 手机号码
        if (TelephonyManager.getLine1Number) {
            TelephonyManager.getLine1Number.implementation = function() {
                var result = this.getLine1Number();
                
                var collection = {
                    type: 'phone_number',
                    value: result,
                    method: 'TelephonyManager.getLine1Number',
                    timestamp: Date.now() - startTime,
                    beforeConsent: !userConsented
                };
                
                dataCollections.push(collection);
                
                if (!userConsented) {
                    addViolation(
                        'premature_data_collection',
                        '在用户同意前收集手机号码',
                        collection,
                        'high'
                    );
                }
                
                log('warning', 'Phone number collected', collection);
                return result;
            };
        }
        
    } catch (e) {
        log('error', 'Failed to hook device info', {error: e.toString()});
    }
}

// 3. 监控位置信息收集
function hookLocation() {
    try {
        var LocationManager = Java.use("android.location.LocationManager");
        
        // getLastKnownLocation
        LocationManager.getLastKnownLocation.implementation = function(provider) {
            var result = this.getLastKnownLocation(provider);
            
            var collection = {
                type: 'location',
                provider: provider,
                method: 'LocationManager.getLastKnownLocation',
                timestamp: Date.now() - startTime,
                beforeConsent: !userConsented
            };
            
            dataCollections.push(collection);
            
            if (!userConsented) {
                addViolation(
                    'premature_data_collection',
                    '在用户同意前收集位置信息',
                    collection,
                    'high'
                );
            }
            
            log('warning', 'Location collected', collection);
            return result;
        };
        
        // requestLocationUpdates
        LocationManager.requestLocationUpdates.overload(
            'java.lang.String', 
            'long', 
            'float', 
            'android.location.LocationListener'
        ).implementation = function(provider, minTime, minDistance, listener) {
            
            var collection = {
                type: 'location_updates',
                provider: provider,
                minTime: minTime,
                minDistance: minDistance,
                method: 'LocationManager.requestLocationUpdates',
                timestamp: Date.now() - startTime,
                beforeConsent: !userConsented
            };
            
            dataCollections.push(collection);
            
            if (!userConsented) {
                addViolation(
                    'premature_data_collection',
                    '在用户同意前请求位置更新',
                    collection,
                    'high'
                );
            }
            
            log('warning', 'Location updates requested', collection);
            return this.requestLocationUpdates(provider, minTime, minDistance, listener);
        };
        
    } catch (e) {
        log('error', 'Failed to hook location', {error: e.toString()});
    }
}

// 4. 监控网络请求
function hookNetwork() {
    try {
        // Hook OkHttp
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        var Request = Java.use("okhttp3.Request");

        // Hook URL连接
        var URL = Java.use("java.net.URL");
        URL.openConnection.overload().implementation = function() {
            var connection = this.openConnection();

            var request = {
                type: 'url_connection',
                url: this.toString(),
                method: 'GET',
                timestamp: Date.now() - startTime
            };

            networkRequests.push(request);
            log('info', 'Network request detected', request);

            return connection;
        };

        // Hook HttpURLConnection
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        HttpURLConnection.connect.implementation = function() {
            var url = this.getURL().toString();
            var method = this.getRequestMethod();

            var request = {
                type: 'http_connection',
                url: url,
                method: method,
                timestamp: Date.now() - startTime
            };

            networkRequests.push(request);
            log('info', 'HTTP connection detected', request);

            return this.connect();
        };

    } catch (e) {
        log('error', 'Failed to hook network', {error: e.toString()});
    }
}

// 5. 监控文件访问
function hookFileAccess() {
    try {
        var File = Java.use("java.io.File");

        // Hook文件列表获取
        File.listFiles.overload().implementation = function() {
            var result = this.listFiles();
            var path = this.getAbsolutePath();

            var access = {
                type: 'file_list',
                path: path,
                method: 'File.listFiles',
                timestamp: Date.now() - startTime,
                beforeConsent: !userConsented
            };

            // 检查是否访问敏感目录
            if (path.includes('/storage/') || path.includes('/sdcard/')) {
                if (!userConsented) {
                    addViolation(
                        'premature_file_access',
                        '在用户同意前访问存储文件',
                        access,
                        'medium'
                    );
                }
            }

            log('debug', 'File access detected', access);
            return result;
        };

    } catch (e) {
        log('error', 'Failed to hook file access', {error: e.toString()});
    }
}

// 6. 监控联系人访问
function hookContacts() {
    try {
        var ContentResolver = Java.use("android.content.ContentResolver");

        ContentResolver.query.overload(
            'android.net.Uri',
            '[Ljava.lang.String;',
            'java.lang.String',
            '[Ljava.lang.String;',
            'java.lang.String;'
        ).implementation = function(uri, projection, selection, selectionArgs, sortOrder) {

            var uriString = uri.toString();

            if (uriString.includes('contacts') || uriString.includes('ContactsContract')) {
                var collection = {
                    type: 'contacts',
                    uri: uriString,
                    method: 'ContentResolver.query',
                    timestamp: Date.now() - startTime,
                    beforeConsent: !userConsented
                };

                dataCollections.push(collection);

                if (!userConsented) {
                    addViolation(
                        'premature_data_collection',
                        '在用户同意前访问联系人',
                        collection,
                        'high'
                    );
                }

                log('warning', 'Contacts access detected', collection);
            }

            return this.query(uri, projection, selection, selectionArgs, sortOrder);
        };

    } catch (e) {
        log('error', 'Failed to hook contacts', {error: e.toString()});
    }
}

// 初始化Hook
Java.perform(function() {
    log('info', 'Privacy compliance detection started');

    // 获取包名
    try {
        var ActivityThread = Java.use("android.app.ActivityThread");
        var currentApplication = ActivityThread.currentApplication();
        if (currentApplication) {
            packageName = currentApplication.getPackageName();
            log('info', 'Target package detected', {packageName: packageName});
        }
    } catch (e) {
        log('error', 'Failed to get package name', {error: e.toString()});
    }

    // 启动各种Hook
    hookPermissions();
    hookDeviceInfo();
    hookLocation();
    hookNetwork();
    hookFileAccess();
    hookContacts();

    log('info', 'All hooks initialized successfully');
});

// 导出函数供Python调用
rpc.exports = {
    getViolations: function() {
        return violations;
    },
    
    getPermissionRequests: function() {
        return permissionRequests;
    },
    
    getDataCollections: function() {
        return dataCollections;
    },
    
    setUserConsent: function(consented) {
        userConsented = consented;
        log('info', 'User consent status updated', {consented: consented});
    },
    
    setPrivacyPolicyShown: function(shown) {
        privacyPolicyShown = shown;
        log('info', 'Privacy policy shown status updated', {shown: shown});
    },
    
    reset: function() {
        violations = [];
        permissionRequests = [];
        dataCollections = [];
        networkRequests = [];
        userConsented = false;
        privacyPolicyShown = false;
        startTime = Date.now();
        log('info', 'Detection state reset');
    }
};
