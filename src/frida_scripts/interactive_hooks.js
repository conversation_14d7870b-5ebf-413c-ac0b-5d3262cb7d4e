/**
 * 交互式检测专用Frida脚本
 * 用于检测用户拒绝权限后的违规行为
 */

// 全局状态
var userConsent = {
    privacyPolicy: null,  // null, 'agree', 'disagree'
    permissions: {}       // 权限状态记录
};

var violations = [];

// 权限相关API监控
function hookPermissionAPIs() {
    console.log("[Interactive] 开始监控权限相关API...");
    
    // 监控位置权限
    hookLocationAPIs();
    
    // 监控联系人权限
    hookContactAPIs();
    
    // 监控电话权限
    hookPhoneAPIs();
    
    // 监控存储权限
    hookStorageAPIs();
    
    // 监控相机权限
    hookCameraAPIs();
    
    // 监控麦克风权限
    hookMicrophoneAPIs();
}

function hookLocationAPIs() {
    try {
        // LocationManager
        var LocationManager = Java.use("android.location.LocationManager");
        
        LocationManager.requestLocationUpdates.overload('java.lang.String', 'long', 'float', 'android.location.LocationListener').implementation = function(provider, minTime, minDistance, listener) {
            console.log("[LOCATION] 检测到位置权限调用: " + provider);
            
            if (userConsent.permissions['location'] === 'denied') {
                var violation = {
                    type: 'unauthorized_location_access',
                    description: '用户拒绝位置权限后，APP仍尝试获取位置信息',
                    timestamp: new Date().toISOString(),
                    details: {
                        provider: provider,
                        minTime: minTime,
                        minDistance: minDistance,
                        stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                    }
                };
                violations.push(violation);
                console.log("[VIOLATION] 位置权限违规: " + JSON.stringify(violation));
            }
            
            return this.requestLocationUpdates(provider, minTime, minDistance, listener);
        };
        
        // GPS Provider
        LocationManager.getLastKnownLocation.implementation = function(provider) {
            console.log("[LOCATION] 检测到获取最后位置: " + provider);
            
            if (userConsent.permissions['location'] === 'denied') {
                var violation = {
                    type: 'unauthorized_last_location_access',
                    description: '用户拒绝位置权限后，APP仍尝试获取最后位置',
                    timestamp: new Date().toISOString(),
                    details: {
                        provider: provider,
                        stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                    }
                };
                violations.push(violation);
                console.log("[VIOLATION] 最后位置违规: " + JSON.stringify(violation));
            }
            
            return this.getLastKnownLocation(provider);
        };
        
    } catch (e) {
        console.log("[ERROR] Hook位置API失败: " + e);
    }
}

function hookContactAPIs() {
    try {
        // ContentResolver for contacts
        var ContentResolver = Java.use("android.content.ContentResolver");
        
        ContentResolver.query.overload('android.net.Uri', '[Ljava.lang.String;', 'java.lang.String', '[Ljava.lang.String;', 'java.lang.String').implementation = function(uri, projection, selection, selectionArgs, sortOrder) {
            var uriStr = uri.toString();
            
            if (uriStr.includes("com.android.contacts") || uriStr.includes("ContactsContract")) {
                console.log("[CONTACTS] 检测到联系人访问: " + uriStr);
                
                if (userConsent.permissions['contacts'] === 'denied') {
                    var violation = {
                        type: 'unauthorized_contacts_access',
                        description: '用户拒绝联系人权限后，APP仍尝试访问联系人',
                        timestamp: new Date().toISOString(),
                        details: {
                            uri: uriStr,
                            projection: projection ? projection.toString() : null,
                            selection: selection,
                            stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                        }
                    };
                    violations.push(violation);
                    console.log("[VIOLATION] 联系人权限违规: " + JSON.stringify(violation));
                }
            }
            
            return this.query(uri, projection, selection, selectionArgs, sortOrder);
        };
        
    } catch (e) {
        console.log("[ERROR] Hook联系人API失败: " + e);
    }
}

function hookPhoneAPIs() {
    try {
        // TelephonyManager
        var TelephonyManager = Java.use("android.telephony.TelephonyManager");
        
        TelephonyManager.getDeviceId.implementation = function() {
            console.log("[PHONE] 检测到获取设备ID");
            
            if (userConsent.permissions['phone'] === 'denied') {
                var violation = {
                    type: 'unauthorized_device_id_access',
                    description: '用户拒绝电话权限后，APP仍尝试获取设备ID',
                    timestamp: new Date().toISOString(),
                    details: {
                        method: 'getDeviceId',
                        stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                    }
                };
                violations.push(violation);
                console.log("[VIOLATION] 设备ID违规: " + JSON.stringify(violation));
            }
            
            return this.getDeviceId();
        };
        
        TelephonyManager.getLine1Number.implementation = function() {
            console.log("[PHONE] 检测到获取电话号码");
            
            if (userConsent.permissions['phone'] === 'denied') {
                var violation = {
                    type: 'unauthorized_phone_number_access',
                    description: '用户拒绝电话权限后，APP仍尝试获取电话号码',
                    timestamp: new Date().toISOString(),
                    details: {
                        method: 'getLine1Number',
                        stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                    }
                };
                violations.push(violation);
                console.log("[VIOLATION] 电话号码违规: " + JSON.stringify(violation));
            }
            
            return this.getLine1Number();
        };
        
    } catch (e) {
        console.log("[ERROR] Hook电话API失败: " + e);
    }
}

function hookStorageAPIs() {
    try {
        // File access monitoring
        var FileInputStream = Java.use("java.io.FileInputStream");
        var FileOutputStream = Java.use("java.io.FileOutputStream");
        
        FileInputStream.$init.overload('java.lang.String').implementation = function(name) {
            if (name.includes("/sdcard/") || name.includes("/storage/")) {
                console.log("[STORAGE] 检测到文件读取: " + name);
                
                if (userConsent.permissions['storage'] === 'denied') {
                    var violation = {
                        type: 'unauthorized_storage_read',
                        description: '用户拒绝存储权限后，APP仍尝试读取外部存储',
                        timestamp: new Date().toISOString(),
                        details: {
                            file: name,
                            operation: 'read',
                            stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                        }
                    };
                    violations.push(violation);
                    console.log("[VIOLATION] 存储读取违规: " + JSON.stringify(violation));
                }
            }
            
            return this.$init(name);
        };
        
    } catch (e) {
        console.log("[ERROR] Hook存储API失败: " + e);
    }
}

function hookCameraAPIs() {
    try {
        var Camera = Java.use("android.hardware.Camera");
        
        Camera.open.overload().implementation = function() {
            console.log("[CAMERA] 检测到相机访问");
            
            if (userConsent.permissions['camera'] === 'denied') {
                var violation = {
                    type: 'unauthorized_camera_access',
                    description: '用户拒绝相机权限后，APP仍尝试打开相机',
                    timestamp: new Date().toISOString(),
                    details: {
                        method: 'Camera.open',
                        stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                    }
                };
                violations.push(violation);
                console.log("[VIOLATION] 相机权限违规: " + JSON.stringify(violation));
            }
            
            return this.open();
        };
        
    } catch (e) {
        console.log("[ERROR] Hook相机API失败: " + e);
    }
}

function hookMicrophoneAPIs() {
    try {
        var AudioRecord = Java.use("android.media.AudioRecord");
        
        AudioRecord.$init.overload('int', 'int', 'int', 'int', 'int').implementation = function(audioSource, sampleRateInHz, channelConfig, audioFormat, bufferSizeInBytes) {
            console.log("[MICROPHONE] 检测到麦克风访问");
            
            if (userConsent.permissions['microphone'] === 'denied') {
                var violation = {
                    type: 'unauthorized_microphone_access',
                    description: '用户拒绝麦克风权限后，APP仍尝试录音',
                    timestamp: new Date().toISOString(),
                    details: {
                        audioSource: audioSource,
                        sampleRate: sampleRateInHz,
                        stackTrace: Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())
                    }
                };
                violations.push(violation);
                console.log("[VIOLATION] 麦克风权限违规: " + JSON.stringify(violation));
            }
            
            return this.$init(audioSource, sampleRateInHz, channelConfig, audioFormat, bufferSizeInBytes);
        };
        
    } catch (e) {
        console.log("[ERROR] Hook麦克风API失败: " + e);
    }
}

// 消息处理
function setupMessageHandling() {
    recv('setUserConsent', function(message) {
        console.log("[Interactive] 收到用户同意状态: " + JSON.stringify(message.payload));
        
        if (message.payload.type === 'privacy_policy') {
            userConsent.privacyPolicy = message.payload.choice;
        } else if (message.payload.type === 'permission') {
            userConsent.permissions[message.payload.permission] = message.payload.choice;
        }
        
        console.log("[Interactive] 当前用户同意状态: " + JSON.stringify(userConsent));
    });
    
    recv('getViolations', function(message) {
        send({
            type: 'violations',
            data: violations
        });
    });
    
    recv('clearViolations', function(message) {
        violations = [];
        console.log("[Interactive] 违规记录已清空");
    });
}

// 主函数
Java.perform(function() {
    console.log("[Interactive] 交互式检测脚本已加载");
    
    // 设置消息处理
    setupMessageHandling();
    
    // 开始监控
    hookPermissionAPIs();
    
    console.log("[Interactive] 所有Hook已设置完成，开始监控...");
});
