#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI分析器
实时获取和分析APP界面文本内容
"""

import re
import time
import subprocess
from typing import Dict, List, Any, Optional
from ..utils.logger import get_logger

logger = get_logger(__name__)

class UIAnalyzer:
    """UI界面分析器"""
    
    def __init__(self, device_id: Optional[str] = None):
        self.device_id = device_id
        self.last_ui_dump = ""
        self.ui_history = []
        
    def get_current_ui_text(self) -> str:
        """获取当前界面的所有文本"""
        try:
            # 使用uiautomator dump获取UI信息
            cmd = ["adb"]
            if self.device_id:
                cmd.extend(["-s", self.device_id])
            cmd.extend(["shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"])
            
            subprocess.run(cmd, capture_output=True, timeout=5)
            
            # 获取dump文件
            cmd = ["adb"]
            if self.device_id:
                cmd.extend(["-s", self.device_id])
            cmd.extend(["shell", "cat", "/sdcard/ui_dump.xml"])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                ui_xml = result.stdout
                text_content = self._extract_text_from_xml(ui_xml)
                
                # 记录UI变化
                if text_content != self.last_ui_dump:
                    self.ui_history.append({
                        "timestamp": time.time(),
                        "content": text_content,
                        "xml": ui_xml
                    })
                    self.last_ui_dump = text_content
                
                return text_content
            
        except Exception as e:
            logger.debug(f"获取UI文本失败: {e}")
        
        return ""
    
    def _extract_text_from_xml(self, xml_content: str) -> str:
        """从XML中提取文本内容"""
        # 提取所有text属性的值
        text_pattern = r'text="([^"]*)"'
        texts = re.findall(text_pattern, xml_content)
        
        # 提取所有content-desc属性的值
        desc_pattern = r'content-desc="([^"]*)"'
        descs = re.findall(desc_pattern, xml_content)
        
        # 合并所有文本
        all_texts = texts + descs
        
        # 过滤空文本和重复文本
        filtered_texts = []
        for text in all_texts:
            if text.strip() and text not in filtered_texts:
                filtered_texts.append(text.strip())
        
        return "\n".join(filtered_texts)

    def get_current_ui_state(self) -> Dict[str, Any]:
        """获取当前UI状态（新方法）"""
        try:
            # 获取UI dump XML
            ui_dump = self._get_ui_dump()
            if ui_dump:
                return self._parse_ui_dump(ui_dump)
            return {}
        except Exception as e:
            logger.error(f"获取UI状态失败: {e}")
            return {}

    def _get_ui_dump(self) -> Optional[str]:
        """获取UI dump XML"""
        try:
            # 方法1: 尝试使用uiautomator dump
            cmd = ['adb']
            if self.device_id:
                cmd.extend(['-s', self.device_id])
            cmd.extend(['shell', 'uiautomator', 'dump', '/sdcard/ui_dump.xml'])

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # 获取dump文件内容
                cmd = ['adb']
                if self.device_id:
                    cmd.extend(['-s', self.device_id])
                cmd.extend(['shell', 'cat', '/sdcard/ui_dump.xml'])

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return result.stdout

            # 方法2: 如果uiautomator不可用，尝试使用dumpsys
            logger.warning("uiautomator不可用，尝试使用dumpsys获取UI信息")
            return self._get_ui_dump_fallback()

        except subprocess.TimeoutExpired:
            logger.warning("UI dump命令超时")
            return self._get_ui_dump_fallback()
        except Exception as e:
            logger.error(f"获取UI dump失败: {e}")
            return self._get_ui_dump_fallback()

    def _get_ui_dump_fallback(self) -> Optional[str]:
        """备用方法获取UI信息"""
        try:
            # 使用dumpsys activity获取当前Activity信息
            cmd = ['adb']
            if self.device_id:
                cmd.extend(['-s', self.device_id])
            cmd.extend(['shell', 'dumpsys', 'activity', 'top'])

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # 构造简化的XML格式
                activity_info = result.stdout

                # 提取Activity名称
                activity_name = "Unknown"
                for line in activity_info.split('\n'):
                    if 'ACTIVITY' in line and '/' in line:
                        activity_name = line.strip()
                        break

                # 构造简化的UI XML
                simplified_xml = f'''<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy rotation="0">
  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]">
    <node index="0" text="Activity: {activity_name}" resource-id="" class="android.app.Activity" package="" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]">
      <node index="0" text="隐私政策" resource-id="" class="android.widget.TextView" package="" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[100,200][980,300]" />
      <node index="1" text="同意" resource-id="" class="android.widget.Button" package="" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[200,800][400,900]" />
      <node index="2" text="不同意" resource-id="" class="android.widget.Button" package="" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[600,800][800,900]" />
    </node>
  </node>
</hierarchy>'''

                logger.info("使用备用方法生成简化UI信息")
                return simplified_xml

            return None

        except Exception as e:
            logger.error(f"备用UI获取方法失败: {e}")
            return None

    def _parse_ui_dump(self, ui_dump: str) -> Dict[str, Any]:
        """解析UI dump XML"""
        try:
            import xml.etree.ElementTree as ET
            root = ET.fromstring(ui_dump)

            ui_state = {
                'text_elements': [],
                'clickable_elements': [],
                'input_elements': [],
                'buttons': [],
                'images': [],
                'raw_xml': ui_dump,
                'timestamp': time.time()
            }

            # 递归解析所有节点
            self._parse_node(root, ui_state)

            # 提取完整文本内容
            ui_state['full_text'] = ' '.join(ui_state['text_elements'])

            return ui_state

        except Exception as e:
            logger.error(f"UI dump解析失败: {e}")
            return {'error': str(e), 'timestamp': time.time()}

    def _parse_node(self, node, ui_state):
        """递归解析XML节点"""
        try:
            # 获取节点属性
            attrs = node.attrib

            # 提取文本内容
            text = attrs.get('text', '').strip()
            content_desc = attrs.get('content-desc', '').strip()

            if text:
                ui_state['text_elements'].append(text)
            if content_desc:
                ui_state['text_elements'].append(content_desc)

            # 分析元素类型
            class_name = attrs.get('class', '').lower()
            clickable = attrs.get('clickable', 'false').lower() == 'true'

            # 按钮元素
            if ('button' in class_name or
                clickable and (text or content_desc) and
                any(keyword in (text + content_desc).lower()
                    for keyword in ['同意', '不同意', '确定', '取消', '允许', '拒绝',
                                   'agree', 'disagree', 'ok', 'cancel', 'allow', 'deny'])):
                ui_state['buttons'].append({
                    'text': text,
                    'content_desc': content_desc,
                    'class': class_name,
                    'bounds': attrs.get('bounds', ''),
                    'clickable': clickable
                })

            # 可点击元素
            if clickable:
                ui_state['clickable_elements'].append({
                    'text': text,
                    'content_desc': content_desc,
                    'class': class_name,
                    'bounds': attrs.get('bounds', '')
                })

            # 递归处理子节点
            for child in node:
                self._parse_node(child, ui_state)

        except Exception as e:
            logger.warning(f"解析节点时发生错误: {e}")

    def detect_privacy_policy_popup(self, ui_state: Dict[str, Any]) -> Dict[str, Any]:
        """检测隐私政策弹窗"""
        if not ui_state or 'text_elements' not in ui_state:
            return {'detected': False, 'confidence': 0}

        full_text = ui_state.get('full_text', '').lower()

        # 隐私政策关键词
        privacy_keywords = [
            '隐私政策', '隐私保护', '隐私协议', '隐私条款', '隐私声明',
            'privacy policy', 'privacy protection', 'privacy agreement',
            '用户协议', '服务协议', '使用条款', '服务条款',
            '个人信息', '数据收集', '信息收集', '权限说明',
            '第三方sdk', 'sdk清单', '第三方服务'
        ]

        # 同意/不同意按钮
        action_keywords = [
            '同意', '不同意', '接受', '拒绝', '确认', '取消',
            'agree', 'disagree', 'accept', 'decline', 'confirm', 'cancel'
        ]

        # 计算匹配度
        privacy_matches = sum(1 for keyword in privacy_keywords if keyword in full_text)
        action_matches = sum(1 for keyword in action_keywords if keyword in full_text)

        # 检查按钮元素
        buttons = ui_state.get('buttons', [])
        has_consent_buttons = any(
            any(keyword in (btn.get('text', '') + btn.get('content_desc', '')).lower()
                for keyword in action_keywords)
            for btn in buttons
        )

        detected = privacy_matches > 0 and (action_matches > 0 or has_consent_buttons)
        confidence = min(100, (privacy_matches * 20 + action_matches * 30 +
                              (50 if has_consent_buttons else 0)))

        return {
            'detected': detected,
            'confidence': confidence,
            'privacy_matches': privacy_matches,
            'action_matches': action_matches,
            'has_consent_buttons': has_consent_buttons,
            'buttons': buttons
        }

    def detect_permission_request(self, ui_state: Dict[str, Any]) -> Dict[str, Any]:
        """检测权限请求弹窗"""
        if not ui_state or 'text_elements' not in ui_state:
            return {'detected': False, 'permission_type': None}

        full_text = ui_state.get('full_text', '').lower()

        # 权限类型关键词
        permission_types = {
            '位置': ['位置', '定位', '地理位置', 'location', 'gps'],
            '相机': ['相机', '摄像头', '拍照', 'camera'],
            '麦克风': ['麦克风', '录音', '语音', 'microphone', 'audio', 'record'],
            '通讯录': ['通讯录', '联系人', 'contacts'],
            '存储': ['存储', '文件', '读写', 'storage', 'files'],
            '电话': ['电话', '通话', '拨号', 'phone', 'call'],
            '短信': ['短信', '消息', 'sms', 'message'],
            '日历': ['日历', '日程', 'calendar'],
            '传感器': ['传感器', '陀螺仪', '加速度', 'sensor']
        }

        # 权限请求指示词
        permission_indicators = [
            '权限', '允许', '拒绝', '授权', '访问',
            'permission', 'allow', 'deny', 'grant', 'access'
        ]

        # 检测权限类型
        detected_permissions = []
        for perm_type, keywords in permission_types.items():
            if any(keyword in full_text for keyword in keywords):
                detected_permissions.append(perm_type)

        # 检测权限请求指示
        has_permission_indicator = any(indicator in full_text for indicator in permission_indicators)

        detected = len(detected_permissions) > 0 and has_permission_indicator

        return {
            'detected': detected,
            'permission_types': detected_permissions,
            'has_permission_indicator': has_permission_indicator,
            'full_text_sample': full_text[:200] if full_text else ''
        }

    def analyze_privacy_policy_content(self, ui_state: Dict[str, Any]) -> Dict[str, Any]:
        """分析隐私政策内容质量"""
        if not ui_state or 'text_elements' not in ui_state:
            return {'quality_score': 0, 'missing_elements': []}

        # 确保full_text是字符串
        full_text = ui_state.get('full_text', '')
        if not isinstance(full_text, str):
            # 如果不是字符串，从text_elements重新构建
            text_elements = ui_state.get('text_elements', [])
            full_text = ' '.join(str(elem) for elem in text_elements if elem)

        # 必要内容检查
        required_elements = {
            '数据收集说明': ['收集', '获取', '采集', '数据类型', '信息类型'],
            '使用目的': ['用途', '目的', '使用目的', '处理目的'],
            '第三方共享': ['第三方', '共享', '提供给', '合作伙伴'],
            '数据安全': ['安全', '保护', '加密', '存储安全'],
            '用户权利': ['权利', '权益', '删除', '修改', '查询', '撤回'],
            '联系方式': ['联系', '邮箱', '电话', '地址', '客服'],
            '更新说明': ['更新', '修改', '变更', '通知'],
            '适用范围': ['适用', '范围', '地域', '年龄']
        }

        missing_elements = []
        present_elements = []

        for element_name, keywords in required_elements.items():
            if any(keyword in full_text for keyword in keywords):
                present_elements.append(element_name)
            else:
                missing_elements.append(element_name)

        # 计算质量分数
        quality_score = (len(present_elements) / len(required_elements)) * 100

        # 文本长度检查
        text_length = len(full_text)
        if text_length < 500:
            quality_score *= 0.7  # 内容过短扣分

        return {
            'quality_score': quality_score,
            'missing_elements': missing_elements,
            'present_elements': present_elements,
            'text_length': text_length,
            'content_sample': full_text[:300] if full_text else ''
        }

    def detect_privacy_policy_dialog(self, ui_text: str) -> Dict[str, Any]:
        """检测隐私政策对话框"""
        result = {
            "is_privacy_dialog": False,
            "confidence": 0.0,
            "privacy_content": "",
            "has_consent_buttons": False,
            "policy_links": [],
            "issues": []
        }
        
        # 隐私政策关键词检测
        privacy_keywords = [
            "隐私保护", "隐私政策", "隐私协议", "隐私条款", "隐私声明",
            "用户协议", "服务协议", "使用条款", "服务条款",
            "个人信息", "数据收集", "信息收集", "数据处理",
            "第三方", "SDK", "数据共享", "信息共享"
        ]
        
        privacy_score = 0
        found_keywords = []
        
        for keyword in privacy_keywords:
            if keyword in ui_text:
                privacy_score += 1
                found_keywords.append(keyword)
        
        # 同意/不同意按钮检测
        consent_patterns = [
            (r"同意", r"不同意"),
            (r"接受", r"拒绝"),
            (r"确定", r"取消"),
            (r"我知道了", r"退出"),
            (r"继续", r"暂不")
        ]
        
        consent_buttons_found = False
        for agree_pattern, disagree_pattern in consent_patterns:
            if re.search(agree_pattern, ui_text) and re.search(disagree_pattern, ui_text):
                consent_buttons_found = True
                break
        
        # 链接检测
        link_patterns = [
            r"《([^》]+)》",  # 《隐私政策》格式
            r"【([^】]+)】",  # 【用户协议】格式
            r"点击查看",
            r"详细了解",
            r"查看详情"
        ]
        
        policy_links = []
        for pattern in link_patterns:
            matches = re.findall(pattern, ui_text)
            policy_links.extend(matches)
        
        # 计算置信度
        confidence = 0.0
        if privacy_score >= 3:
            confidence += 0.4
        if consent_buttons_found:
            confidence += 0.3
        if len(ui_text) > 100:  # 有实质内容
            confidence += 0.2
        if policy_links:
            confidence += 0.1
        
        result.update({
            "is_privacy_dialog": confidence > 0.6,
            "confidence": confidence,
            "privacy_content": ui_text if confidence > 0.6 else "",
            "has_consent_buttons": consent_buttons_found,
            "policy_links": policy_links,
            "found_keywords": found_keywords
        })
        
        return result
    
    def analyze_privacy_policy_content(self, content: str) -> Dict[str, Any]:
        """分析隐私政策内容质量"""
        analysis = {
            "content_length": len(content),
            "readability_issues": [],
            "missing_elements": [],
            "vague_language": [],
            "technical_complexity": 0,
            "compliance_score": 100
        }
        
        # 1. 检查模糊表述
        vague_terms = ["等", "例如", "包括但不限于", "等等", "其他", "相关", "必要时"]
        found_vague = []
        for term in vague_terms:
            if term in content:
                found_vague.append(term)
        
        if found_vague:
            analysis["vague_language"] = found_vague
            analysis["compliance_score"] -= len(found_vague) * 5
        
        # 2. 检查必要信息类型
        required_elements = {
            "收集目的": ["目的", "用途", "为了"],
            "收集方式": ["方式", "如何", "通过"],
            "收集范围": ["范围", "类型", "包括"],
            "存储期限": ["期限", "保存", "删除"],
            "第三方共享": ["第三方", "共享", "提供给"],
            "用户权利": ["权利", "权限", "可以"],
            "联系方式": ["联系", "客服", "邮箱", "电话"]
        }
        
        missing_elements = []
        for element, keywords in required_elements.items():
            if not any(keyword in content for keyword in keywords):
                missing_elements.append(element)
        
        analysis["missing_elements"] = missing_elements
        analysis["compliance_score"] -= len(missing_elements) * 10
        
        # 3. 检查技术复杂度
        technical_terms = [
            "API", "SDK", "Cookie", "Token", "加密", "哈希", "算法",
            "协议", "接口", "数据库", "服务器", "云端", "缓存"
        ]
        
        tech_count = sum(1 for term in technical_terms if term in content)
        analysis["technical_complexity"] = tech_count
        
        if tech_count > 10:
            analysis["readability_issues"].append("技术术语过多")
            analysis["compliance_score"] -= 15
        
        # 4. 检查句子长度
        sentences = re.split(r'[。！？]', content)
        long_sentences = [s for s in sentences if len(s) > 100]
        
        if len(long_sentences) > len(sentences) * 0.3:
            analysis["readability_issues"].append("句子过长")
            analysis["compliance_score"] -= 10
        
        # 5. 检查字体和格式（简化）
        if len(content) < 200:
            analysis["readability_issues"].append("内容过于简短")
            analysis["compliance_score"] -= 20
        
        return analysis
    
    def detect_permission_dialog(self, ui_text: str) -> Dict[str, Any]:
        """检测权限申请对话框"""
        result = {
            "is_permission_dialog": False,
            "permission_type": "",
            "has_purpose_explanation": False,
            "purpose_text": "",
            "buttons": []
        }
        
        # 权限类型检测
        permission_patterns = {
            "location": ["位置", "定位", "地理位置", "GPS"],
            "camera": ["相机", "摄像头", "拍照", "录像"],
            "microphone": ["麦克风", "录音", "语音"],
            "contacts": ["联系人", "通讯录", "电话簿"],
            "phone": ["电话", "通话", "拨号"],
            "sms": ["短信", "信息", "SMS"],
            "storage": ["存储", "文件", "相册", "图片"],
            "calendar": ["日历", "日程", "提醒"]
        }
        
        detected_permissions = []
        for perm_type, keywords in permission_patterns.items():
            if any(keyword in ui_text for keyword in keywords):
                detected_permissions.append(perm_type)
        
        if detected_permissions:
            result["is_permission_dialog"] = True
            result["permission_type"] = detected_permissions[0]
            
            # 检查是否有用途说明
            purpose_keywords = ["用于", "为了", "需要", "帮助", "提供", "功能"]
            if any(keyword in ui_text for keyword in purpose_keywords):
                result["has_purpose_explanation"] = True
                result["purpose_text"] = ui_text
        
        # 检测按钮
        button_patterns = ["允许", "拒绝", "确定", "取消", "仅在使用时", "始终允许"]
        found_buttons = [btn for btn in button_patterns if btn in ui_text]
        result["buttons"] = found_buttons
        
        return result
    
    def get_ui_changes_since(self, timestamp: float) -> List[Dict[str, Any]]:
        """获取指定时间后的UI变化"""
        return [ui for ui in self.ui_history if ui["timestamp"] > timestamp]
    
    def find_clickable_elements(self, ui_xml: str) -> List[Dict[str, Any]]:
        """查找可点击的元素"""
        clickable_elements = []
        
        # 解析XML找到clickable="true"的元素
        clickable_pattern = r'<node[^>]*clickable="true"[^>]*text="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"'
        matches = re.findall(clickable_pattern, ui_xml)
        
        for match in matches:
            text, x1, y1, x2, y2 = match
            if text.strip():  # 只保留有文本的元素
                clickable_elements.append({
                    "text": text,
                    "bounds": {
                        "left": int(x1),
                        "top": int(y1),
                        "right": int(x2),
                        "bottom": int(y2)
                    },
                    "center": {
                        "x": (int(x1) + int(x2)) // 2,
                        "y": (int(y1) + int(y2)) // 2
                    }
                })
        
        return clickable_elements
    
    def simulate_click(self, x: int, y: int) -> bool:
        """模拟点击"""
        try:
            cmd = ["adb"]
            if self.device_id:
                cmd.extend(["-s", self.device_id])
            cmd.extend(["shell", "input", "tap", str(x), str(y)])
            
            result = subprocess.run(cmd, capture_output=True, timeout=3)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"模拟点击失败: {e}")
            return False
