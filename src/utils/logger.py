#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional
from colorama import Fore, Back, Style, init

# 初始化colorama
init(autoreset=True)

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Back.WHITE + Style.BRIGHT
    }
    
    def format(self, record):
        # 获取原始格式化结果
        log_message = super().format(record)
        
        # 添加颜色
        level_color = self.COLORS.get(record.levelname, '')
        if level_color:
            # 只给级别名称添加颜色
            colored_level = f"{level_color}{record.levelname}{Style.RESET_ALL}"
            log_message = log_message.replace(record.levelname, colored_level)
        
        return log_message

def setup_logger(name: str = "privacy_detector", 
                log_level: str = "INFO",
                log_file: Optional[str] = None,
                console_output: bool = True) -> logging.Logger:
    """设置日志记录器"""
    
    # 创建logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    colored_formatter = ColoredFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台输出
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(colored_formatter)
        logger.addHandler(console_handler)
    
    # 文件输出
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    else:
        # 默认日志文件
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d")
        default_log_file = os.path.join(log_dir, f"privacy_detector_{timestamp}.log")
        
        file_handler = logging.FileHandler(default_log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def get_logger(name: str = None) -> logging.Logger:
    """获取日志记录器"""
    if name is None:
        name = "privacy_detector"
    
    logger = logging.getLogger(name)
    
    # 如果logger还没有处理器，则设置默认配置
    if not logger.handlers:
        return setup_logger(name)
    
    return logger

class DetectionLogger:
    """检测专用日志记录器"""
    
    def __init__(self, app_name: str, session_id: str):
        self.app_name = app_name
        self.session_id = session_id
        self.logger = get_logger(f"detection_{app_name}_{session_id}")
        
        # 创建专用日志文件
        log_dir = "logs/detection"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"{app_name}_{timestamp}.log")
        
        # 添加文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
            '%(asctime)s - [%(levelname)s] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def log_violation(self, violation_type: str, description: str, evidence: dict):
        """记录违规行为"""
        self.logger.warning(f"VIOLATION - {violation_type}: {description}")
        self.logger.debug(f"Evidence: {evidence}")
    
    def log_permission_request(self, permissions: list, context: str):
        """记录权限申请"""
        self.logger.info(f"PERMISSION_REQUEST - {permissions} in context: {context}")
    
    def log_data_collection(self, data_type: str, method: str, details: dict):
        """记录数据收集"""
        self.logger.info(f"DATA_COLLECTION - {data_type} via {method}")
        self.logger.debug(f"Details: {details}")
    
    def log_network_request(self, url: str, method: str, data_sent: bool):
        """记录网络请求"""
        data_indicator = "with_data" if data_sent else "no_data"
        self.logger.info(f"NETWORK_REQUEST - {method} {url} ({data_indicator})")
    
    def log_ui_event(self, event_type: str, details: dict):
        """记录UI事件"""
        self.logger.debug(f"UI_EVENT - {event_type}: {details}")
    
    def log_sdk_activity(self, sdk_name: str, activity_type: str, details: dict):
        """记录SDK活动"""
        self.logger.info(f"SDK_ACTIVITY - {sdk_name}: {activity_type}")
        self.logger.debug(f"Details: {details}")

# 全局日志实例
_global_logger = None

def init_global_logger(log_level: str = "INFO", log_file: str = None):
    """初始化全局日志记录器"""
    global _global_logger
    _global_logger = setup_logger("privacy_detector_global", log_level, log_file)
    return _global_logger

def get_global_logger() -> logging.Logger:
    """获取全局日志记录器"""
    global _global_logger
    if _global_logger is None:
        _global_logger = setup_logger("privacy_detector_global")
    return _global_logger

# 便捷函数
def log_info(message: str, logger_name: str = None):
    """记录信息日志"""
    logger = get_logger(logger_name) if logger_name else get_global_logger()
    logger.info(message)

def log_warning(message: str, logger_name: str = None):
    """记录警告日志"""
    logger = get_logger(logger_name) if logger_name else get_global_logger()
    logger.warning(message)

def log_error(message: str, logger_name: str = None):
    """记录错误日志"""
    logger = get_logger(logger_name) if logger_name else get_global_logger()
    logger.error(message)

def log_debug(message: str, logger_name: str = None):
    """记录调试日志"""
    logger = get_logger(logger_name) if logger_name else get_global_logger()
    logger.debug(message)
