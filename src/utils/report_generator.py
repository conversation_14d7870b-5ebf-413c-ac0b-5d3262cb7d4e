#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器
生成详细的Excel格式隐私合规检测报告
"""

import os
import time
from datetime import datetime
from typing import List, Dict, Any
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from .logger import get_logger

logger = get_logger(__name__)

class ReportGenerator:
    """隐私合规检测报告生成器"""
    
    def __init__(self, output_dir: str = "./reports"):
        self.output_dir = output_dir
        self.ensure_output_dir()
        
        # 违规严重程度颜色映射
        self.severity_colors = {
            'high': 'FFCCCC',      # 红色
            'medium': 'FFFFCC',    # 黄色
            'low': 'CCFFCC'        # 绿色
        }
        
        # 合规项目映射
        self.compliance_items = {
            1: "无隐私政策或规则缺失",
            2: "首次运行未提示隐私政策",
            3: "隐私政策访问路径过深",
            4: "隐私政策难以阅读",
            5: "未逐项说明收集信息",
            6: "变更规则未通知用户",
            7: "敏感权限未同步告知目的",
            8: "规则内容晦涩难懂",
            9: "提前收集信息或权限",
            10: "拒绝后继续收集或频繁骚扰",
            11: "超范围收集信息或权限",
            12: "非明示同意（默认勾选）",
            13: "擅自恢复默认权限",
            14: "未提供非定向推送选项",
            15: "欺诈诱骗同意",
            16: "未提供撤回同意渠道",
            17: "违反声明的收集规则",
            18: "收集与业务无关的信息",
            19: "拒绝非必要权限即禁用功能",
            20: "新增功能超原有同意范围",
            21: "超频次收集信息",
            22: "以'改善体验'强制收集",
            23: "一次性索要多个权限",
            24: "未经同意向第三方提供信息",
            25: "第三方应用共享未授权",
            26: "未提供更正/删除/注销功能",
            27: "设置注销/更正不合理条件",
            28: "未及时响应用户操作",
            29: "前端操作未同步后台",
            30: "未建立投诉渠道或超期处理"
        }
    
    def generate_report(self, detection_results: Dict[str, Any], 
                       app_info: Dict[str, Any]) -> str:
        """生成完整的检测报告"""
        logger.info("开始生成隐私合规检测报告")
        
        # 创建工作簿
        wb = Workbook()
        
        # 删除默认工作表
        wb.remove(wb.active)
        
        # 创建各个工作表
        self._create_summary_sheet(wb, detection_results, app_info)
        self._create_violations_sheet(wb, detection_results)
        self._create_timeline_sheet(wb, detection_results)
        self._create_details_sheet(wb, detection_results)
        self._create_recommendations_sheet(wb, detection_results)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        app_name = app_info.get('package_name', 'unknown_app')
        filename = f"privacy_compliance_report_{app_name}_{timestamp}.xlsx"
        filepath = os.path.join(self.output_dir, filename)
        
        # 保存文件
        wb.save(filepath)
        logger.info(f"报告已生成: {filepath}")
        
        return filepath
    
    def _create_summary_sheet(self, wb: Workbook, results: Dict[str, Any], 
                             app_info: Dict[str, Any]):
        """创建摘要工作表"""
        ws = wb.create_sheet("检测摘要", 0)
        
        # 设置标题
        ws['A1'] = "APP隐私合规检测报告"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:E1')
        
        # 基本信息
        row = 3
        basic_info = [
            ("APP名称", app_info.get('app_name', '未知')),
            ("包名", app_info.get('package_name', '未知')),
            ("版本", app_info.get('version', '未知')),
            ("检测时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            ("检测设备", app_info.get('device_info', '未知')),
            ("检测时长", f"{app_info.get('detection_duration', 0):.1f}秒")
        ]
        
        for label, value in basic_info:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            ws[f'A{row}'].font = Font(bold=True)
            row += 1
        
        # 检测结果统计
        row += 2
        ws[f'A{row}'] = "检测结果统计"
        ws[f'A{row}'].font = Font(size=14, bold=True)
        row += 1
        
        # 统计违规数量
        all_violations = []
        for category_results in results.values():
            if isinstance(category_results, list):
                all_violations.extend(category_results)
        
        severity_counts = {'high': 0, 'medium': 0, 'low': 0}
        for violation in all_violations:
            severity = violation.get('severity', 'medium')
            severity_counts[severity] += 1
        
        stats = [
            ("总违规项数", len(all_violations)),
            ("高风险违规", severity_counts['high']),
            ("中风险违规", severity_counts['medium']),
            ("低风险违规", severity_counts['low']),
            ("合规率", f"{((30 - len(all_violations)) / 30 * 100):.1f}%")
        ]
        
        for label, value in stats:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            ws[f'A{row}'].font = Font(bold=True)
            
            # 根据违规严重程度设置颜色
            if "高风险" in label and value > 0:
                ws[f'B{row}'].fill = PatternFill(start_color=self.severity_colors['high'], 
                                               end_color=self.severity_colors['high'], 
                                               fill_type='solid')
            elif "中风险" in label and value > 0:
                ws[f'B{row}'].fill = PatternFill(start_color=self.severity_colors['medium'], 
                                               end_color=self.severity_colors['medium'], 
                                               fill_type='solid')
            
            row += 1
        
        # 设置列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 30
    
    def _create_violations_sheet(self, wb: Workbook, results: Dict[str, Any]):
        """创建违规详情工作表"""
        ws = wb.create_sheet("违规详情")
        
        # 设置表头
        headers = [
            "序号", "违规类型", "合规项目", "描述", "严重程度", 
            "发现时间", "证据摘要", "调用栈", "建议措施"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')
        
        # 收集所有违规
        all_violations = []
        for category_results in results.values():
            if isinstance(category_results, list):
                all_violations.extend(category_results)
        
        # 按严重程度排序
        severity_order = {'high': 0, 'medium': 1, 'low': 2}
        all_violations.sort(key=lambda x: severity_order.get(x.get('severity', 'medium'), 1))
        
        # 填充数据
        for row, violation in enumerate(all_violations, 2):
            compliance_item = violation.get('compliance_item', 0)
            compliance_desc = self.compliance_items.get(compliance_item, f"项目{compliance_item}")
            
            # 格式化时间
            timestamp = violation.get('timestamp', time.time())
            formatted_time = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
            
            # 提取证据摘要
            evidence = violation.get('evidence', {})
            evidence_summary = self._format_evidence_summary(evidence)
            
            # 提取调用栈
            stack_trace = violation.get('stackTrace', [])
            stack_summary = self._format_stack_trace(stack_trace)
            
            # 生成建议措施
            recommendation = self._generate_recommendation(violation)
            
            data = [
                violation.get('id', row-1),
                violation.get('type', '未知'),
                f"{compliance_item}. {compliance_desc}",
                violation.get('description', ''),
                violation.get('severity', 'medium'),
                formatted_time,
                evidence_summary,
                stack_summary,
                recommendation
            ]
            
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=str(value))
                
                # 根据严重程度设置背景色
                severity = violation.get('severity', 'medium')
                if severity in self.severity_colors:
                    cell.fill = PatternFill(start_color=self.severity_colors[severity], 
                                          end_color=self.severity_colors[severity], 
                                          fill_type='solid')
                
                # 设置文本换行
                cell.alignment = Alignment(wrap_text=True, vertical='top')
        
        # 设置列宽
        column_widths = [8, 25, 30, 40, 12, 20, 30, 25, 35]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col)].width = width
        
        # 设置边框
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in ws.iter_rows(min_row=1, max_row=len(all_violations)+1, 
                               min_col=1, max_col=len(headers)):
            for cell in row:
                cell.border = thin_border
    
    def _create_timeline_sheet(self, wb: Workbook, results: Dict[str, Any]):
        """创建时间线工作表"""
        ws = wb.create_sheet("检测时间线")
        
        # 设置表头
        headers = ["时间", "事件类型", "描述", "严重程度", "相关数据"]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 收集所有事件并按时间排序
        events = []
        for category_results in results.values():
            if isinstance(category_results, list):
                for item in category_results:
                    events.append(item)
        
        events.sort(key=lambda x: x.get('timestamp', 0))
        
        # 填充数据
        for row, event in enumerate(events, 2):
            timestamp = event.get('timestamp', time.time())
            formatted_time = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            
            data = [
                formatted_time,
                event.get('type', '未知事件'),
                event.get('description', ''),
                event.get('severity', 'info'),
                str(event.get('evidence', {}))[:100] + "..." if len(str(event.get('evidence', {}))) > 100 else str(event.get('evidence', {}))
            ]
            
            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)
        
        # 设置列宽
        ws.column_dimensions['A'].width = 25
        ws.column_dimensions['B'].width = 20
        ws.column_dimensions['C'].width = 40
        ws.column_dimensions['D'].width = 12
        ws.column_dimensions['E'].width = 50
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def _format_evidence_summary(self, evidence: Dict[str, Any]) -> str:
        """格式化证据摘要"""
        if not evidence:
            return "无"
        
        summary_parts = []
        for key, value in evidence.items():
            if isinstance(value, (list, dict)):
                summary_parts.append(f"{key}: {len(value) if isinstance(value, list) else '复杂对象'}")
            else:
                summary_parts.append(f"{key}: {str(value)[:50]}")
        
        return "; ".join(summary_parts[:3])  # 只显示前3个关键信息
    
    def _format_stack_trace(self, stack_trace: List[Any]) -> str:
        """格式化调用栈"""
        if not stack_trace:
            return "无"
        
        # 只显示前3层调用栈
        formatted_stack = []
        for frame in stack_trace[:3]:
            if isinstance(frame, dict):
                class_name = frame.get('className', '')
                method_name = frame.get('methodName', '')
                formatted_stack.append(f"{class_name}.{method_name}")
            else:
                formatted_stack.append(str(frame)[:50])
        
        return " -> ".join(formatted_stack)
    
    def _generate_recommendation(self, violation: Dict[str, Any]) -> str:
        """生成建议措施"""
        violation_type = violation.get('type', '')
        compliance_item = violation.get('compliance_item', 0)
        
        recommendations = {
            'missing_privacy_policy': '添加完整的隐私政策文档',
            'no_first_run_prompt': '在首次启动时显示隐私政策同意弹窗',
            'premature_collection': '在用户同意隐私政策后再收集数据',
            'unauthorized_third_party_sharing': '获得用户明确同意后再向第三方传输数据',
            'missing_user_rights_functions': '在设置中添加数据管理功能'
        }
        
        return recommendations.get(violation_type, '请参考相关法规要求进行整改')

    def _create_details_sheet(self, wb: Workbook, results: Dict[str, Any]):
        """创建详细数据工作表"""
        ws = wb.create_sheet("详细数据")

        # 设置表头
        headers = ["数据类型", "详细信息", "时间戳", "来源", "风险等级"]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        row = 2

        # 处理各类检测数据
        for category, data in results.items():
            if isinstance(data, list):
                for item in data:
                    evidence = item.get('evidence', {})

                    # 展开证据中的详细信息
                    for key, value in evidence.items():
                        if isinstance(value, list):
                            for sub_item in value:
                                ws.cell(row=row, column=1, value=f"{category}.{key}")
                                ws.cell(row=row, column=2, value=str(sub_item))
                                ws.cell(row=row, column=3, value=datetime.fromtimestamp(
                                    item.get('timestamp', time.time())).strftime("%Y-%m-%d %H:%M:%S"))
                                ws.cell(row=row, column=4, value=item.get('type', '未知'))
                                ws.cell(row=row, column=5, value=item.get('severity', 'medium'))
                                row += 1
                        else:
                            ws.cell(row=row, column=1, value=f"{category}.{key}")
                            ws.cell(row=row, column=2, value=str(value))
                            ws.cell(row=row, column=3, value=datetime.fromtimestamp(
                                item.get('timestamp', time.time())).strftime("%Y-%m-%d %H:%M:%S"))
                            ws.cell(row=row, column=4, value=item.get('type', '未知'))
                            ws.cell(row=row, column=5, value=item.get('severity', 'medium'))
                            row += 1

        # 设置列宽
        ws.column_dimensions['A'].width = 25
        ws.column_dimensions['B'].width = 50
        ws.column_dimensions['C'].width = 20
        ws.column_dimensions['D'].width = 20
        ws.column_dimensions['E'].width = 12

    def _create_recommendations_sheet(self, wb: Workbook, results: Dict[str, Any]):
        """创建整改建议工作表"""
        ws = wb.create_sheet("整改建议")

        # 设置标题
        ws['A1'] = "隐私合规整改建议"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:C1')

        row = 3

        # 收集所有违规并按合规项目分组
        violations_by_item = {}
        for category_results in results.values():
            if isinstance(category_results, list):
                for violation in category_results:
                    item = violation.get('compliance_item', 0)
                    if item not in violations_by_item:
                        violations_by_item[item] = []
                    violations_by_item[item].append(violation)

        # 生成针对性建议
        for item_num in sorted(violations_by_item.keys()):
            if item_num == 0:
                continue

            violations = violations_by_item[item_num]
            item_desc = self.compliance_items.get(item_num, f"项目{item_num}")

            # 项目标题
            ws[f'A{row}'] = f"合规项目{item_num}: {item_desc}"
            ws[f'A{row}'].font = Font(size=12, bold=True)
            ws[f'A{row}'].fill = PatternFill(start_color='E6E6FA', end_color='E6E6FA', fill_type='solid')
            ws.merge_cells(f'A{row}:C{row}')
            row += 1

            # 问题描述
            ws[f'A{row}'] = "发现问题:"
            ws[f'A{row}'].font = Font(bold=True)
            row += 1

            for violation in violations:
                ws[f'B{row}'] = f"• {violation.get('description', '')}"
                row += 1

            # 整改建议
            ws[f'A{row}'] = "整改建议:"
            ws[f'A{row}'].font = Font(bold=True)
            row += 1

            recommendations = self._get_detailed_recommendations(item_num, violations)
            for rec in recommendations:
                ws[f'B{row}'] = f"• {rec}"
                row += 1

            # 优先级
            max_severity = max([v.get('severity', 'low') for v in violations])
            priority = {'high': '高', 'medium': '中', 'low': '低'}[max_severity]

            ws[f'A{row}'] = "整改优先级:"
            ws[f'A{row}'].font = Font(bold=True)
            ws[f'B{row}'] = priority
            ws[f'B{row}'].fill = PatternFill(start_color=self.severity_colors[max_severity],
                                           end_color=self.severity_colors[max_severity],
                                           fill_type='solid')
            row += 2

        # 设置列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 60
        ws.column_dimensions['C'].width = 20

    def _get_detailed_recommendations(self, compliance_item: int, violations: List[Dict[str, Any]]) -> List[str]:
        """获取详细的整改建议"""
        recommendations_map = {
            1: [
                "制定完整的隐私政策文档",
                "确保隐私政策涵盖所有数据收集和使用场景",
                "定期更新隐私政策内容"
            ],
            2: [
                "在APP首次启动时显示隐私政策同意弹窗",
                "确保用户必须主动同意才能继续使用",
                "提供清晰的同意和拒绝选项"
            ],
            3: [
                "将隐私政策入口放在主界面或设置的显著位置",
                "确保访问隐私政策不超过4次点击",
                "在用户协议页面提供直接链接"
            ],
            9: [
                "在用户同意隐私政策之前不收集任何个人信息",
                "延迟权限申请到实际需要使用时",
                "实现权限的动态申请机制"
            ],
            10: [
                "尊重用户的拒绝选择，不再重复申请被拒绝的权限",
                "设置合理的权限申请间隔（建议24小时以上）",
                "提供权限说明和替代方案"
            ],
            24: [
                "获得用户明确同意后再向第三方传输数据",
                "对传输的数据进行匿名化处理",
                "建立第三方数据传输审计机制"
            ],
            26: [
                "在设置中添加'个人信息管理'功能",
                "提供数据查看、修改、删除选项",
                "实现账户注销功能"
            ]
        }

        return recommendations_map.get(compliance_item, [
            "请参考《个人信息保护法》相关要求",
            "咨询法律专业人士获取具体指导",
            "参考行业最佳实践进行整改"
        ])

    def generate_summary_report(self, detection_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成摘要报告（用于API返回）"""
        all_violations = []
        for category_results in detection_results.values():
            if isinstance(category_results, list):
                all_violations.extend(category_results)

        # 统计违规情况
        severity_counts = {'high': 0, 'medium': 0, 'low': 0}
        compliance_violations = {}

        for violation in all_violations:
            severity = violation.get('severity', 'medium')
            severity_counts[severity] += 1

            compliance_item = violation.get('compliance_item', 0)
            if compliance_item not in compliance_violations:
                compliance_violations[compliance_item] = []
            compliance_violations[compliance_item].append(violation)

        return {
            'total_violations': len(all_violations),
            'severity_breakdown': severity_counts,
            'compliance_rate': (30 - len(all_violations)) / 30 * 100,
            'violated_items': list(compliance_violations.keys()),
            'high_risk_violations': [v for v in all_violations if v.get('severity') == 'high'],
            'recommendations_count': len(compliance_violations)
        }
