#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进检测器的导入
"""

import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'src'))

print("🔍 测试改进检测器导入...")
print(f"当前目录: {current_dir}")
print(f"Python路径: {sys.path[:3]}")

# 测试导入
try:
    print("\n1. 测试直接导入...")
    from src.improved_detector import ImprovedPrivacyDetector
    print("✅ 直接导入成功")
    
    # 测试初始化
    detector = ImprovedPrivacyDetector("com.test.app")
    print("✅ 初始化成功")
    
except ImportError as e:
    print(f"❌ 直接导入失败: {e}")
    
    try:
        print("\n2. 测试相对导入...")
        import src.improved_detector
        ImprovedPrivacyDetector = src.improved_detector.ImprovedPrivacyDetector
        print("✅ 相对导入成功")
        
        # 测试初始化
        detector = ImprovedPrivacyDetector("com.test.app")
        print("✅ 初始化成功")
        
    except Exception as e2:
        print(f"❌ 相对导入也失败: {e2}")

# 测试GUI导入逻辑
print("\n3. 测试GUI导入逻辑...")
try:
    # 模拟GUI的导入逻辑
    project_root = current_dir
    sys.path.insert(0, project_root)
    sys.path.insert(0, os.path.join(project_root, 'src'))
    
    try:
        from improved_detector import ImprovedPrivacyDetector as GUI_ImprovedDetector
        print("✅ GUI导入方式1成功")
    except ImportError:
        from src.improved_detector import ImprovedPrivacyDetector as GUI_ImprovedDetector
        print("✅ GUI导入方式2成功")
    
    # 测试初始化
    gui_detector = GUI_ImprovedDetector("com.test.app")
    print("✅ GUI检测器初始化成功")
    
except Exception as e:
    print(f"❌ GUI导入逻辑失败: {e}")

print("\n🎉 导入测试完成")
