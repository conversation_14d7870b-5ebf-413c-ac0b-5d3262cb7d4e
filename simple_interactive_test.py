#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的交互式检测测试
专门解决您遇到的问题
"""

import sys
import os
import time
import tkinter as tk
import tkinter.messagebox as msgbox

# 添加路径
sys.path.insert(0, '.')
sys.path.insert(0, 'src')

class SimpleInteractiveDetector:
    """简化的交互式检测器"""
    
    def __init__(self, package_name):
        self.package_name = package_name
        self.detection_active = True
        
    def run_detection(self):
        """运行简化的交互式检测"""
        print(f"🚀 开始交互式检测: {self.package_name}")
        
        # 步骤1: 提示用户启动APP
        msgbox.showinfo(
            "🎯 交互式检测开始",
            f"📱 目标APP: {self.package_name}\n\n"
            "请按照以下步骤操作：\n\n"
            "1️⃣ 手动启动目标APP\n"
            "2️⃣ 观察是否出现隐私政策弹窗\n"
            "3️⃣ 不要点击任何按钮，等待检测\n\n"
            "点击确定后开始检测..."
        )
        
        # 步骤2: 等待并检测隐私政策
        privacy_result = self._detect_privacy_policy()
        
        if privacy_result['found']:
            print("✅ 检测到隐私政策弹窗")
            
            # 步骤3: 等待用户选择
            user_choice = self._wait_for_user_choice()
            
            if user_choice == 'agree':
                print("👍 用户选择同意，开始同意后检测...")
                return self._detect_after_agreement()
            elif user_choice == 'disagree':
                print("👎 用户选择不同意，开始违规监控...")
                return self._detect_after_disagreement()
            else:
                print("⏭️ 用户跳过检测")
                return {'skipped': True}
        else:
            print("❌ 未检测到隐私政策弹窗")
            return {
                'success': False,
                'error': '未检测到隐私政策弹窗',
                'violations': [
                    {
                        'type': 'missing_privacy_policy',
                        'description': 'APP启动时未显示隐私政策弹窗',
                        'severity': 'high'
                    }
                ]
            }
    
    def _detect_privacy_policy(self):
        """检测隐私政策弹窗"""
        from src.utils.ui_analyzer import UIAnalyzer
        
        ui_analyzer = UIAnalyzer()
        
        # 给用户时间启动APP
        time.sleep(3)
        
        # 持续检测60秒
        for attempt in range(60):
            try:
                print(f"[{attempt+1}/60] 正在检测隐私政策弹窗...")
                
                ui_state = ui_analyzer.get_current_ui_state()
                
                if ui_state and 'text_elements' in ui_state:
                    # 检测隐私政策
                    privacy_result = ui_analyzer.detect_privacy_policy_popup(ui_state)
                    
                    if privacy_result['detected'] and privacy_result['confidence'] > 50:
                        # 让用户确认
                        text_sample = ' '.join(ui_state.get('text_elements', []))[:200]
                        
                        confirm = msgbox.askyesno(
                            "🔍 检测到可能的隐私政策",
                            f"检测到可能的隐私政策弹窗！\n\n"
                            f"置信度: {privacy_result['confidence']}%\n"
                            f"隐私关键词: {privacy_result['privacy_matches']} 个\n"
                            f"操作按钮: {privacy_result['action_matches']} 个\n\n"
                            f"内容预览:\n{text_sample}...\n\n"
                            f"这是隐私政策弹窗吗？"
                        )
                        
                        if confirm:
                            return {
                                'found': True,
                                'confidence': privacy_result['confidence'],
                                'content': text_sample
                            }
                
                time.sleep(1)
                
            except Exception as e:
                print(f"检测错误: {e}")
                time.sleep(1)
        
        # 60秒后询问用户
        user_confirm = msgbox.askyesnocancel(
            "⏰ 检测超时",
            "60秒内未自动检测到隐私政策弹窗。\n\n"
            "请确认当前APP状态：\n\n"
            "• 点击'是'：APP确实显示了隐私政策弹窗\n"
            "• 点击'否'：APP没有显示隐私政策弹窗\n"
            "• 点击'取消'：重新检测\n\n"
            "您看到隐私政策弹窗了吗？"
        )
        
        if user_confirm is True:
            return {'found': True, 'user_confirmed': True}
        elif user_confirm is None:
            return self._detect_privacy_policy()  # 重新检测
        else:
            return {'found': False}
    
    def _wait_for_user_choice(self):
        """等待用户选择"""
        msgbox.showinfo(
            "📋 请在APP中操作",
            "✅ 已确认存在隐私政策弹窗！\n\n"
            "现在请在APP中进行选择：\n\n"
            "• 仔细阅读隐私政策内容\n"
            "• 选择'同意'或'不同意'\n"
            "• 完成选择后回到此对话框\n\n"
            "⚠️ 请先在APP中操作，再点击确定！"
        )
        
        while True:
            choice = msgbox.askyesnocancel(
                "🔍 确认您的选择",
                "请告诉检测器您在APP中的选择：\n\n"
                "• 点击'是'：我选择了'同意/接受'\n"
                "• 点击'否'：我选择了'不同意/拒绝'\n"
                "• 点击'取消'：我需要更多时间\n\n"
                "您在APP中选择了什么？"
            )
            
            if choice is True:
                confirm = msgbox.askyesno("确认", "确认您选择了'同意'？")
                if confirm:
                    return 'agree'
            elif choice is False:
                confirm = msgbox.askyesno("确认", "确认您选择了'不同意'？")
                if confirm:
                    return 'disagree'
            else:
                more_time = msgbox.askyesno(
                    "需要更多时间？",
                    "是否需要更多时间在APP中操作？\n\n"
                    "点击'是'继续等待，点击'否'跳过检测。"
                )
                if not more_time:
                    return 'skip'
                
                msgbox.showinfo("继续等待", "请继续在APP中操作，完成后回到此对话框。")
    
    def _detect_after_agreement(self):
        """用户同意后的检测"""
        msgbox.showinfo(
            "🔄 持续监控开始",
            "用户已同意隐私政策！\n\n"
            "检测器将持续监控：\n"
            "• 权限请求是否合理\n"
            "• 数据收集是否符合政策\n"
            "• 第三方SDK行为\n\n"
            "请正常使用APP，检测器在后台运行。\n"
            "点击确定开始监控..."
        )
        
        # 模拟持续监控30秒
        start_time = time.time()
        violations = []
        
        while time.time() - start_time < 30 and self.detection_active:
            # 这里可以添加实际的权限监控逻辑
            time.sleep(2)
            print(f"[监控中] 已运行 {int(time.time() - start_time)} 秒...")
        
        msgbox.showinfo(
            "✅ 监控完成",
            f"持续监控已完成！\n\n"
            f"监控时长: {int(time.time() - start_time)} 秒\n"
            f"发现违规: {len(violations)} 个\n\n"
            f"检测报告已生成。"
        )
        
        return {
            'success': True,
            'mode': 'agreement_monitoring',
            'duration': time.time() - start_time,
            'violations': violations
        }
    
    def _detect_after_disagreement(self):
        """用户拒绝后的检测"""
        msgbox.showinfo(
            "🚨 违规监控开始",
            "用户已拒绝隐私政策！\n\n"
            "检测器将监控违规行为：\n"
            "• 未经同意收集数据\n"
            "• 强制要求权限\n"
            "• 限制APP功能\n\n"
            "请继续使用APP，检测器会记录违规行为。\n"
            "点击确定开始监控..."
        )
        
        # 模拟违规监控30秒
        start_time = time.time()
        violations = []
        
        while time.time() - start_time < 30 and self.detection_active:
            # 这里可以添加实际的违规检测逻辑
            time.sleep(2)
            print(f"[违规监控] 已运行 {int(time.time() - start_time)} 秒...")
        
        msgbox.showinfo(
            "🚨 违规监控完成",
            f"违规监控已完成！\n\n"
            f"监控时长: {int(time.time() - start_time)} 秒\n"
            f"发现违规: {len(violations)} 个\n\n"
            f"违规报告已生成。"
        )
        
        return {
            'success': True,
            'mode': 'violation_monitoring',
            'duration': time.time() - start_time,
            'violations': violations
        }

def main():
    """主函数"""
    print("🧪 简化交互式检测测试")
    print("=" * 50)
    
    # 使用您测试的APP包名
    package_name = "com.ivy.qtbz"
    
    print(f"📱 目标APP: {package_name}")
    print("🎯 这个测试将验证真正的交互式检测流程")
    
    # 创建检测器
    detector = SimpleInteractiveDetector(package_name)
    
    try:
        # 运行检测
        result = detector.run_detection()
        
        print("\n📊 检测结果:")
        print("=" * 30)
        
        if result.get('success'):
            print(f"✅ 检测成功")
            print(f"🔧 检测模式: {result.get('mode', 'unknown')}")
            print(f"⏱️ 检测时长: {result.get('duration', 0):.1f} 秒")
            print(f"🚨 发现违规: {len(result.get('violations', []))} 个")
        elif result.get('skipped'):
            print("⏭️ 检测被跳过")
        else:
            print(f"❌ 检测失败: {result.get('error', '未知错误')}")
            
    except KeyboardInterrupt:
        print("\n⏹️ 检测被用户中断")
        detector.detection_active = False
    except Exception as e:
        print(f"\n❌ 检测过程中发生错误: {e}")

if __name__ == "__main__":
    main()
