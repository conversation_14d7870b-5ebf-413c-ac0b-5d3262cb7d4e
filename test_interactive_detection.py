#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交互式检测功能
"""

import sys
import os
import time

# 添加路径
sys.path.insert(0, '.')
sys.path.insert(0, 'src')

def test_interactive_gui():
    """测试交互式GUI检测"""
    print("🧪 测试交互式GUI检测功能")
    print("=" * 50)
    
    try:
        # 导入GUI模块
        from src.gui.main_window import ImprovedPrivacyDetector
        
        print("✅ 成功导入改进的检测器")
        
        # 创建检测器实例
        detector = ImprovedPrivacyDetector(
            package_name="com.ivy.qtbz",  # 使用您测试的APP
            device_id=None,
            interactive_mode=True,
            output_dir="./test_reports"
        )
        
        print("✅ 检测器初始化成功")
        print(f"📱 目标APP: {detector.package_name}")
        print(f"🔧 交互模式: {detector.interactive_mode}")
        
        # 测试UI分析器
        print("\n🔍 测试UI分析器...")
        ui_analyzer = detector.ui_analyzer
        
        print("📱 获取当前UI状态...")
        ui_state = ui_analyzer.get_current_ui_state()
        
        if ui_state and 'error' not in ui_state:
            print(f"✅ UI状态获取成功，发现 {len(ui_state.get('text_elements', []))} 个文本元素")
            
            # 测试隐私政策检测
            privacy_result = ui_analyzer.detect_privacy_policy_popup(ui_state)
            print(f"🔍 隐私政策检测结果: {privacy_result}")
            
            # 测试权限请求检测
            permission_result = ui_analyzer.detect_permission_request(ui_state)
            print(f"🔍 权限请求检测结果: {permission_result}")
            
        else:
            print("⚠️ 无法获取UI状态，可能设备未连接或APP未运行")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_privacy_policy_detection():
    """测试隐私政策检测逻辑"""
    print("\n🔍 测试隐私政策检测逻辑")
    print("=" * 50)
    
    try:
        from src.utils.ui_analyzer import UIAnalyzer
        
        ui_analyzer = UIAnalyzer()
        
        # 模拟UI状态数据
        mock_ui_state = {
            'text_elements': [
                '隐私保护提示',
                '欢迎来到我们的应用！',
                '为了更好地为您提供服务，在使用我们的产品前，请您仔细阅读',
                '《隐私政策》',
                '《用户协议》',
                '请您充分了解我们如何收集、使用、存储和保护您的个人信息',
                '同意',
                '不同意'
            ],
            'buttons': [
                {'text': '同意', 'content_desc': '', 'clickable': True},
                {'text': '不同意', 'content_desc': '', 'clickable': True}
            ],
            'full_text': '隐私保护提示 欢迎来到我们的应用！ 为了更好地为您提供服务，在使用我们的产品前，请您仔细阅读 《隐私政策》 《用户协议》 请您充分了解我们如何收集、使用、存储和保护您的个人信息 同意 不同意'
        }
        
        # 测试隐私政策检测
        result = ui_analyzer.detect_privacy_policy_popup(mock_ui_state)
        
        print(f"✅ 隐私政策检测结果:")
        print(f"   - 检测到: {result['detected']}")
        print(f"   - 置信度: {result['confidence']}%")
        print(f"   - 隐私关键词匹配: {result['privacy_matches']}")
        print(f"   - 操作按钮匹配: {result['action_matches']}")
        print(f"   - 有同意按钮: {result['has_consent_buttons']}")
        
        if result['detected'] and result['confidence'] > 70:
            print("🎯 检测逻辑正常，能够识别隐私政策弹窗")
        else:
            print("⚠️ 检测逻辑可能需要调整")
        
        # 测试内容质量分析
        quality_result = ui_analyzer.analyze_privacy_policy_content(mock_ui_state)
        print(f"\n📊 隐私政策内容质量分析:")
        print(f"   - 质量分数: {quality_result['quality_score']:.1f}%")
        print(f"   - 缺失要素: {quality_result['missing_elements']}")
        print(f"   - 包含要素: {quality_result['present_elements']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 隐私政策检测测试失败: {e}")
        return False

def test_permission_detection():
    """测试权限检测逻辑"""
    print("\n🔍 测试权限检测逻辑")
    print("=" * 50)
    
    try:
        from src.utils.ui_analyzer import UIAnalyzer
        
        ui_analyzer = UIAnalyzer()
        
        # 模拟权限请求UI状态
        mock_permission_ui = {
            'text_elements': [
                '权限请求',
                '应用需要访问您的位置信息',
                '用于提供基于位置的服务',
                '允许',
                '拒绝'
            ],
            'buttons': [
                {'text': '允许', 'content_desc': '', 'clickable': True},
                {'text': '拒绝', 'content_desc': '', 'clickable': True}
            ],
            'full_text': '权限请求 应用需要访问您的位置信息 用于提供基于位置的服务 允许 拒绝'
        }
        
        # 测试权限检测
        result = ui_analyzer.detect_permission_request(mock_permission_ui)
        
        print(f"✅ 权限请求检测结果:")
        print(f"   - 检测到: {result['detected']}")
        print(f"   - 权限类型: {result['permission_types']}")
        print(f"   - 有权限指示词: {result['has_permission_indicator']}")
        
        if result['detected'] and '位置' in result['permission_types']:
            print("🎯 权限检测逻辑正常，能够识别位置权限请求")
        else:
            print("⚠️ 权限检测逻辑可能需要调整")
        
        return True
        
    except Exception as e:
        print(f"❌ 权限检测测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试交互式检测功能")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试交互式GUI
    results.append(("交互式GUI检测", test_interactive_gui()))
    
    # 2. 测试隐私政策检测
    results.append(("隐私政策检测逻辑", test_privacy_policy_detection()))
    
    # 3. 测试权限检测
    results.append(("权限检测逻辑", test_permission_detection()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！交互式检测功能正常")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    print("\n💡 使用建议:")
    print("1. 确保Android设备已连接并启用USB调试")
    print("2. 确保目标APP已安装并可以启动")
    print("3. 确保Frida服务端在设备上正常运行")
    print("4. 使用GUI界面选择'改进模式'进行交互式检测")

if __name__ == "__main__":
    main()
