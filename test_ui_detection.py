#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI检测功能
验证是否能正确识别隐私政策弹窗
"""

import sys
import os
import time

# 添加路径
sys.path.insert(0, '.')
sys.path.insert(0, 'src')

def test_ui_analyzer():
    """测试UI分析器"""
    print("🔍 测试UI分析器功能")
    print("=" * 50)
    
    try:
        from src.utils.ui_analyzer import UIAnalyzer
        
        print("✅ 正在初始化UI分析器...")
        ui_analyzer = UIAnalyzer()
        
        print("📱 请确保您的Android设备已连接并启动了目标APP")
        input("按回车键开始检测当前UI状态...")
        
        print("🔍 正在获取UI状态...")
        ui_state = ui_analyzer.get_current_ui_state()
        
        if ui_state and 'error' not in ui_state:
            print(f"✅ 成功获取UI状态")
            print(f"📊 UI元素统计:")
            print(f"   - 文本元素: {len(ui_state.get('text_elements', []))}")
            print(f"   - 可点击元素: {len(ui_state.get('clickable_elements', []))}")
            print(f"   - 按钮元素: {len(ui_state.get('buttons', []))}")
            
            # 显示文本内容
            text_elements = ui_state.get('text_elements', [])
            if text_elements:
                print(f"\n📝 检测到的文本内容:")
                for i, text in enumerate(text_elements[:10]):  # 只显示前10个
                    print(f"   {i+1}. {text}")
                if len(text_elements) > 10:
                    print(f"   ... 还有 {len(text_elements) - 10} 个文本元素")
            
            # 显示按钮信息
            buttons = ui_state.get('buttons', [])
            if buttons:
                print(f"\n🔘 检测到的按钮:")
                for i, btn in enumerate(buttons):
                    text = btn.get('text', '')
                    desc = btn.get('content_desc', '')
                    print(f"   {i+1}. 文本: '{text}' 描述: '{desc}'")
            
            # 测试隐私政策检测
            print(f"\n🔍 测试隐私政策检测...")
            privacy_result = ui_analyzer.detect_privacy_policy_popup(ui_state)
            
            print(f"📊 隐私政策检测结果:")
            print(f"   - 检测到: {privacy_result['detected']}")
            print(f"   - 置信度: {privacy_result['confidence']}%")
            print(f"   - 隐私关键词匹配: {privacy_result['privacy_matches']}")
            print(f"   - 操作按钮匹配: {privacy_result['action_matches']}")
            print(f"   - 有同意按钮: {privacy_result['has_consent_buttons']}")
            
            if privacy_result['detected']:
                print("🎯 ✅ 检测到隐私政策弹窗！")
            else:
                print("❌ 未检测到隐私政策弹窗")
                
                # 分析为什么没检测到
                full_text = ui_state.get('full_text', '').lower()
                print(f"\n🔍 分析检测失败原因:")
                print(f"   - 总文本长度: {len(full_text)}")
                
                privacy_keywords = ['隐私', 'privacy', '个人信息', '数据', '协议', 'policy']
                found_keywords = [kw for kw in privacy_keywords if kw in full_text]
                print(f"   - 找到的隐私关键词: {found_keywords}")
                
                action_keywords = ['同意', '不同意', 'agree', 'disagree', '确定', '取消']
                found_actions = [kw for kw in action_keywords if kw in full_text]
                print(f"   - 找到的操作关键词: {found_actions}")
            
            # 测试权限检测
            print(f"\n🔍 测试权限请求检测...")
            permission_result = ui_analyzer.detect_permission_request(ui_state)
            
            print(f"📊 权限请求检测结果:")
            print(f"   - 检测到: {permission_result['detected']}")
            print(f"   - 权限类型: {permission_result['permission_types']}")
            print(f"   - 有权限指示词: {permission_result['has_permission_indicator']}")
            
            return True
            
        else:
            print("❌ 无法获取UI状态")
            print("可能的原因:")
            print("1. Android设备未连接")
            print("2. USB调试未启用")
            print("3. adb命令不可用")
            print("4. 目标APP未运行")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_ui_content():
    """手动测试UI内容检测"""
    print("\n🔍 手动UI内容检测")
    print("=" * 50)
    
    # 模拟您看到的隐私政策弹窗内容
    mock_privacy_content = """
    隐私保护提示
    欢迎来到我们的应用！
    为了更好地为您提供服务，在使用我们的产品前，请您仔细阅读
    《隐私政策》及《用户协议》，请您充分了解我们如何收集、使用、存储和保护您的个人信息，并不会收集与所提供服务无关的个人信息。
    根据相关法规要求，当您点击"同意"时，即表示您已阅读并同意上述协议，可以开始使用我们的产品功能；以及功能配套所需的权限申请可以正常进行。
    同意
    不同意
    """
    
    try:
        from src.utils.ui_analyzer import UIAnalyzer
        
        ui_analyzer = UIAnalyzer()
        
        # 构造模拟UI状态
        mock_ui_state = {
            'text_elements': [line.strip() for line in mock_privacy_content.strip().split('\n') if line.strip()],
            'buttons': [
                {'text': '同意', 'content_desc': '', 'clickable': True},
                {'text': '不同意', 'content_desc': '', 'clickable': True}
            ],
            'full_text': mock_privacy_content.replace('\n', ' ').strip()
        }
        
        print("📝 模拟的隐私政策内容:")
        for i, text in enumerate(mock_ui_state['text_elements']):
            print(f"   {i+1}. {text}")
        
        # 测试检测
        result = ui_analyzer.detect_privacy_policy_popup(mock_ui_state)
        
        print(f"\n📊 检测结果:")
        print(f"   - 检测到: {result['detected']}")
        print(f"   - 置信度: {result['confidence']}%")
        print(f"   - 隐私关键词匹配: {result['privacy_matches']}")
        print(f"   - 操作按钮匹配: {result['action_matches']}")
        print(f"   - 有同意按钮: {result['has_consent_buttons']}")
        
        if result['detected'] and result['confidence'] > 70:
            print("🎯 ✅ 检测逻辑正常！能够正确识别隐私政策内容")
        else:
            print("❌ 检测逻辑有问题，需要调整")
        
        # 测试内容质量
        quality_result = ui_analyzer.analyze_privacy_policy_content(mock_ui_state)
        print(f"\n📊 内容质量分析:")
        print(f"   - 质量分数: {quality_result['quality_score']:.1f}%")
        print(f"   - 包含要素: {quality_result['present_elements']}")
        print(f"   - 缺失要素: {quality_result['missing_elements']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 UI检测功能测试")
    print("=" * 60)
    
    print("此测试将验证UI分析器是否能正确检测隐私政策弹窗")
    print("请确保：")
    print("1. Android设备已连接")
    print("2. USB调试已启用")
    print("3. 目标APP已启动并显示隐私政策弹窗")
    print()
    
    # 测试1: 手动内容测试
    print("🧪 测试1: 验证检测逻辑")
    result1 = test_manual_ui_content()
    
    # 测试2: 实际UI检测
    print("\n🧪 测试2: 实际UI检测")
    result2 = test_ui_analyzer()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    if result1:
        print("✅ 检测逻辑正常")
    else:
        print("❌ 检测逻辑有问题")
    
    if result2:
        print("✅ 实际UI检测成功")
    else:
        print("❌ 实际UI检测失败")
    
    if result1 and result2:
        print("\n🎉 所有测试通过！UI检测功能正常")
        print("现在可以使用交互式检测功能")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
        
        if not result1:
            print("- 检测逻辑需要优化")
        if not result2:
            print("- 设备连接或APP状态需要检查")

if __name__ == "__main__":
    main()
