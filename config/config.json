{"detection_settings": {"timeout": 300, "max_violations_per_type": 100, "enable_screenshot": true, "enable_network_monitoring": true, "enable_file_monitoring": true}, "frida_settings": {"spawn_timeout": 10, "attach_timeout": 5, "script_timeout": 30}, "report_settings": {"format": "xlsx", "include_screenshots": true, "include_call_stack": true, "include_timeline": true, "language": "zh-CN"}, "sensitive_apis": {"location": ["android.location.LocationManager.getLastKnownLocation", "android.location.LocationManager.requestLocationUpdates", "com.google.android.gms.location.FusedLocationProviderClient.getLastLocation"], "contacts": ["android.provider.ContactsContract", "android.content.ContentResolver.query"], "phone": ["android.telephony.TelephonyManager.getDeviceId", "android.telephony.TelephonyManager.getImei", "android.telephony.TelephonyManager.getLine1Number"], "storage": ["android.os.Environment.getExternalStorageDirectory", "java.io.File.listFiles"], "camera": ["android.hardware.Camera.open", "android.hardware.camera2.CameraManager.openCamera"], "microphone": ["android.media.AudioRecord.startRecording", "android.media.MediaRecorder.start"]}, "third_party_sdks": ["com.google.android.gms", "com.facebook.ads", "com.tencent.mm", "com.alibaba", "com.baidu", "com.umeng", "com.sina.weibo"]}