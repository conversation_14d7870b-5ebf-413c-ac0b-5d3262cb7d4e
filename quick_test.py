#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试改进的检测器
"""

import sys
import os
sys.path.insert(0, '.')
sys.path.insert(0, 'src')

def test_ui_analyzer_basic():
    """基础UI分析器测试"""
    print("🧪 测试UI分析器基础功能...")
    
    try:
        from src.utils.ui_analyzer import UIAnalyzer
        
        # 初始化
        ui_analyzer = UIAnalyzer()
        print("✅ UI分析器初始化成功")
        
        # 测试UI状态获取
        ui_state = ui_analyzer.get_current_ui_state()
        
        if ui_state and 'error' not in ui_state:
            print("✅ UI状态获取成功")
            print(f"   文本元素: {len(ui_state.get('text_elements', []))} 个")
            print(f"   可点击元素: {len(ui_state.get('clickable_elements', []))} 个")
            
            # 测试隐私政策检测
            policy_result = ui_analyzer.detect_privacy_policy_popup(ui_state)
            print(f"   隐私政策检测: {'发现' if policy_result['detected'] else '未发现'}")
            print(f"   检测置信度: {policy_result['confidence']}%")
            
            # 测试权限请求检测
            permission_result = ui_analyzer.detect_permission_request(ui_state)
            print(f"   权限请求检测: {'发现' if permission_result['detected'] else '未发现'}")
            
            return True
        else:
            print("⚠️ 未获取到UI状态（设备未连接或APP未运行）")
            return False
            
    except Exception as e:
        print(f"❌ UI分析器测试失败: {e}")
        return False

def test_interactive_detector_init():
    """测试交互式检测器初始化"""
    print("\n🧪 测试交互式检测器初始化...")
    
    try:
        from src.core.interactive_detector import InteractivePrivacyDetector
        from src.utils.ui_analyzer import UIAnalyzer
        
        # 创建模拟对象
        class MockSession:
            def create_script(self, script): 
                return MockScript()
            def detach(self): 
                pass
        
        class MockScript:
            def on(self, event, handler): 
                pass
            def load(self): 
                pass
            def unload(self): 
                pass
        
        class MockDevice:
            name = "测试设备"
            type = "usb"
        
        ui_analyzer = UIAnalyzer()
        detector = InteractivePrivacyDetector(
            MockSession(), MockDevice(), {}, ui_analyzer
        )
        
        print("✅ 交互式检测器初始化成功")
        
        # 测试检测结果结构
        results = detector.get_detection_results()
        expected_keys = ['privacy_policy_violations', 'permission_violations', 
                        'sdk_violations', 'user_rights_violations']
        
        if all(key in results for key in expected_keys):
            print("✅ 检测结果结构正确")
            return True
        else:
            print("❌ 检测结果结构不完整")
            return False
            
    except Exception as e:
        print(f"❌ 交互式检测器测试失败: {e}")
        return False

def test_improved_detector_init():
    """测试改进检测器初始化"""
    print("\n🧪 测试改进检测器初始化...")
    
    try:
        from src.improved_detector import ImprovedPrivacyDetector
        
        detector = ImprovedPrivacyDetector(
            package_name="com.test.app",
            interactive_mode=True
        )
        
        print("✅ 改进检测器初始化成功")
        print(f"   包名: {detector.package_name}")
        print(f"   交互模式: {detector.interactive_mode}")
        print(f"   输出目录: {detector.output_dir}")
        
        # 检查组件初始化
        if detector.ui_analyzer:
            print("✅ UI分析器组件已初始化")
        
        if detector.detection_results:
            print("✅ 检测结果结构已初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ 改进检测器测试失败: {e}")
        return False

def show_improvement_summary():
    """显示改进摘要"""
    print("\n📋 改进摘要")
    print("=" * 50)
    
    improvements = [
        "✅ 实时UI文本识别和分析",
        "✅ 智能隐私政策弹窗检测",
        "✅ 交互式用户操作检测",
        "✅ 差异化检测路径（同意/拒绝）",
        "✅ 持续权限监控",
        "✅ 大幅减少误报率"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    print("\n🎯 解决的问题:")
    solved_issues = [
        "❌ → ✅ 有隐私政策却报告缺失",
        "❌ → ✅ 首次弹窗显示却报告未提示", 
        "❌ → ✅ 访问路径检测不准确",
        "❌ → ✅ 缺乏用户交互检测"
    ]
    
    for issue in solved_issues:
        print(f"   {issue}")

def main():
    """主测试函数"""
    print("🚀 快速测试改进的隐私合规检测器")
    print("=" * 60)
    
    # 运行测试
    test_results = []
    
    test_results.append(test_ui_analyzer_basic())
    test_results.append(test_interactive_detector_init())
    test_results.append(test_improved_detector_init())
    
    # 显示测试结果
    print("\n📊 测试结果")
    print("=" * 30)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！改进功能正常工作")
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
    
    # 显示改进摘要
    show_improvement_summary()
    
    print("\n💡 使用建议:")
    print("1. 确保设备已连接并启动目标APP")
    print("2. 使用交互式模式获得最佳检测效果")
    print("3. 根据提示进行用户操作")
    print("4. 查看详细的检测报告")
    
    print("\n🔧 启动命令:")
    print("python src/improved_detector.py <包名>")

if __name__ == '__main__':
    main()
