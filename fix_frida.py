#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Frida环境修复脚本
自动检测和修复Frida连接问题
"""

import os
import sys
import subprocess
import urllib.request
import platform

def print_banner():
    """打印横幅"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    Frida环境修复工具                          ║
╚══════════════════════════════════════════════════════════════╝
    """)

def check_adb():
    """检查ADB连接"""
    print("🔍 检查ADB连接...")
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ ADB命令执行失败")
            return False
        
        devices = [line for line in result.stdout.split('\n') if '\tdevice' in line]
        if not devices:
            print("❌ 未检测到Android设备")
            print("请确保：")
            print("  1. 设备已连接USB")
            print("  2. 启用了USB调试")
            print("  3. 授权了调试权限")
            return False
        
        print(f"✅ 检测到 {len(devices)} 个设备")
        for device in devices:
            device_id = device.split('\t')[0]
            print(f"   - {device_id}")
        
        return True
        
    except FileNotFoundError:
        print("❌ 未找到ADB命令")
        print("请安装Android SDK Platform Tools")
        return False

def get_device_arch():
    """获取设备架构"""
    print("\n🔍 检查设备架构...")
    try:
        result = subprocess.run(
            ["adb", "shell", "getprop", "ro.product.cpu.abi"], 
            capture_output=True, text=True
        )
        
        if result.returncode == 0:
            arch = result.stdout.strip()
            print(f"✅ 设备架构: {arch}")
            return arch
        else:
            print("❌ 无法获取设备架构")
            return None
            
    except Exception as e:
        print(f"❌ 获取架构失败: {e}")
        return None

def check_frida_server():
    """检查Frida服务端状态"""
    print("\n🔍 检查Frida服务端...")
    
    # 检查是否已安装
    result = subprocess.run(
        ["adb", "shell", "ls", "/data/local/tmp/frida-server"], 
        capture_output=True, text=True
    )
    
    if result.returncode != 0:
        print("❌ Frida服务端未安装")
        return False
    
    print("✅ Frida服务端已安装")
    
    # 检查是否运行
    result = subprocess.run(
        ["adb", "shell", "ps", "|", "grep", "frida-server"], 
        capture_output=True, text=True, shell=True
    )
    
    if "frida-server" in result.stdout:
        print("✅ Frida服务端正在运行")
        return True
    else:
        print("⚠️  Frida服务端未运行")
        return False

def start_frida_server():
    """启动Frida服务端"""
    print("\n🚀 启动Frida服务端...")
    
    # 尝试启动
    commands = [
        ["adb", "shell", "su", "-c", "/data/local/tmp/frida-server &"],
        ["adb", "shell", "/data/local/tmp/frida-server &"],
        ["adb", "shell", "su", "-c", "nohup /data/local/tmp/frida-server > /dev/null 2>&1 &"]
    ]
    
    for cmd in commands:
        try:
            print(f"尝试命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            
            # 等待一下让服务启动
            import time
            time.sleep(2)
            
            # 检查是否启动成功
            if check_frida_connection():
                print("✅ Frida服务端启动成功")
                return True
                
        except subprocess.TimeoutExpired:
            print("⚠️  命令超时，可能已在后台启动")
            if check_frida_connection():
                return True
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            continue
    
    print("❌ 所有启动方式都失败了")
    return False

def check_frida_connection():
    """检查Frida连接"""
    try:
        import frida
        devices = frida.enumerate_devices()
        usb_devices = [d for d in devices if d.type == 'usb']
        
        if usb_devices:
            print(f"✅ Frida连接正常，发现 {len(usb_devices)} 个USB设备")
            return True
        else:
            print("❌ 未发现USB设备")
            return False
            
    except Exception as e:
        print(f"❌ Frida连接失败: {e}")
        return False

def download_frida_server(arch):
    """下载Frida服务端"""
    print(f"\n📥 下载Frida服务端 ({arch})...")
    
    # 架构映射
    arch_mapping = {
        'arm64-v8a': 'android-arm64',
        'armeabi-v7a': 'android-arm',
        'x86_64': 'android-x86_64',
        'x86': 'android-x86'
    }
    
    if arch not in arch_mapping:
        print(f"❌ 不支持的架构: {arch}")
        return False
    
    frida_arch = arch_mapping[arch]
    
    # 获取最新版本（简化版本，实际应该从API获取）
    version = "16.1.4"  # 可以从 frida.__version__ 获取
    
    url = f"https://github.com/frida/frida/releases/download/{version}/frida-server-{version}-{frida_arch}.xz"
    filename = f"frida-server-{frida_arch}.xz"
    
    print(f"下载地址: {url}")
    print("注意: 由于网络原因，可能需要手动下载")
    print(f"请访问: https://github.com/frida/frida/releases")
    print(f"下载: frida-server-{version}-{frida_arch}.xz")
    print("然后解压并重命名为 frida-server")
    
    return False  # 暂时返回False，让用户手动下载

def install_frida_server():
    """安装Frida服务端"""
    print("\n📦 安装Frida服务端...")
    
    # 检查本地是否有frida-server文件
    if not os.path.exists("frida-server"):
        print("❌ 未找到frida-server文件")
        print("请先下载对应架构的frida-server")
        return False
    
    try:
        # 推送到设备
        print("推送文件到设备...")
        subprocess.run(["adb", "push", "frida-server", "/data/local/tmp/frida-server"], check=True)
        
        # 设置权限
        print("设置执行权限...")
        subprocess.run(["adb", "shell", "chmod", "755", "/data/local/tmp/frida-server"], check=True)
        
        print("✅ Frida服务端安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False

def fix_frida_issues():
    """修复常见Frida问题"""
    print("\n🔧 修复常见问题...")
    
    # 杀死可能冲突的进程
    subprocess.run(["adb", "shell", "su", "-c", "killall frida-server"], 
                  capture_output=True)
    
    # 清理端口
    subprocess.run(["adb", "shell", "su", "-c", "netstat -tulpn | grep :27042"], 
                  capture_output=True)
    
    # 重新转发端口
    subprocess.run(["adb", "forward", "--remove-all"], capture_output=True)
    subprocess.run(["adb", "forward", "tcp:27042", "tcp:27042"], capture_output=True)
    
    print("✅ 常见问题修复完成")

def main():
    """主函数"""
    print_banner()

    # 架构映射
    arch_mapping = {
        'arm64-v8a': 'android-arm64',
        'armeabi-v7a': 'android-arm',
        'x86_64': 'android-x86_64',
        'x86': 'android-x86'
    }

    # 1. 检查ADB
    if not check_adb():
        return

    # 2. 获取设备架构
    arch = get_device_arch()
    if not arch:
        return

    # 3. 检查Frida服务端
    server_installed = check_frida_server()

    # 4. 如果未安装，尝试安装
    if not server_installed:
        print("\n需要安装Frida服务端")

        if not os.path.exists("frida-server"):
            print("请手动下载frida-server:")
            print(f"1. 访问: https://github.com/frida/frida/releases")
            print(f"2. 下载对应架构的文件: frida-server-*-{arch_mapping.get(arch, arch)}.xz")
            print(f"3. 解压并重命名为 frida-server")
            print(f"4. 放在当前目录下")
            print(f"5. 重新运行此脚本")

            # 提供直接下载链接
            frida_arch = arch_mapping.get(arch, arch)
            print(f"\n💡 直接下载链接 (需要替换版本号):")
            print(f"https://github.com/frida/frida/releases/download/16.1.4/frida-server-16.1.4-{frida_arch}.xz")
            return

        if not install_frida_server():
            return

    # 5. 修复常见问题
    fix_frida_issues()

    # 6. 启动服务端
    if not start_frida_server():
        print("\n❌ 自动启动失败，请手动启动:")
        print("adb shell su -c '/data/local/tmp/frida-server &'")
        return

    # 7. 验证连接
    if check_frida_connection():
        print("\n🎉 Frida环境修复成功！")
        print("现在可以运行隐私合规检测工具了")
    else:
        print("\n❌ 修复失败，请检查:")
        print("1. 设备是否已Root")
        print("2. Frida服务端架构是否匹配")
        print("3. 是否有权限执行")

if __name__ == "__main__":
    main()
