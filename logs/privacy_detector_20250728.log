2025-07-28 17:22:55 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 17:22:55 - src.utils.report_generator - INFO - 报告已生成: ./test_reports/privacy_compliance_report_com.performance.test_20250728_172255.xlsx
2025-07-28 17:22:55 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:22:55 - test_logger - INFO - Info message
2025-07-28 17:22:55 - test_logger - WARNING - Warning message
2025-07-28 17:22:55 - test_logger - ERROR - Error message
2025-07-28 17:22:55 - src.detectors.permission_detector - INFO - 开始权限和数据收集合规检测
2025-07-28 17:22:55 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 在用户同意隐私政策前收集个人信息或申请权限
2025-07-28 17:22:55 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 收集device_id信息超出用户授权范围
2025-07-28 17:22:55 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 利用算法定向推送时未提供关闭个性化推荐的选项
2025-07-28 17:22:55 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 未在APP中提供便捷的撤回同意路径
2025-07-28 17:22:55 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 收集的信息与现有功能无关
2025-07-28 17:22:55 - src.detectors.permission_detector - INFO - 权限和数据收集检测完成，发现 5 个违规项
2025-07-28 17:22:55 - src.detectors.privacy_policy_detector - INFO - 开始隐私政策合规检测
2025-07-28 17:22:55 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP中未发现隐私政策
2025-07-28 17:23:00 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP首次运行时未通过弹窗等明显方式提示用户阅读隐私政策
2025-07-28 17:23:00 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 访问隐私政策需要超过4次点击（实际需要5次）
2025-07-28 17:23:00 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 缺少隐私政策变更通知机制
2025-07-28 17:23:00 - src.detectors.privacy_policy_detector - INFO - 隐私政策检测完成，发现 4 个违规项
2025-07-28 17:23:00 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 17:23:00 - src.utils.report_generator - INFO - 报告已生成: ./test_reports/privacy_compliance_report_com.test.app_20250728_172300.xlsx
2025-07-28 17:23:00 - src.detectors.sdk_detector - INFO - 开始第三方SDK合规检测
2025-07-28 17:23:00 - src.detectors.sdk_detector - WARNING - 发现第三方SDK违规: 未匿名化且未经用户同意，通过SDK等向第三方传输个人信息
2025-07-28 17:23:00 - src.detectors.sdk_detector - INFO - 第三方SDK检测完成，发现 1 个违规项
2025-07-28 17:23:00 - src.detectors.user_rights_detector - INFO - 开始用户权利保障合规检测
2025-07-28 17:23:00 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: APP内缺少用户权利功能: 修改资料, 删除数据, 注销账号
2025-07-28 17:23:00 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: 未建立投诉渠道或投诉处理超期
2025-07-28 17:23:00 - src.detectors.user_rights_detector - INFO - 用户权利保障检测完成，发现 2 个违规项
2025-07-28 17:25:21 - privacy_detector - INFO - 启动GUI界面
2025-07-28 17:26:18 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:26:18 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:26:18 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:26:18 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:26:18 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:26:38 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:26:38 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:26:38 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:26:38 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:26:38 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:26:46 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:26:46 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:26:46 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:26:46 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:26:46 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:28:18 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:28:18 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:28:18 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:28:18 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:28:18 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:28:21 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:28:21 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:28:21 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:28:21 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:28:21 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:28:28 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:28:28 - src.core.detector - INFO - 开始检测APP: 青藤壁纸
2025-07-28 17:28:28 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:28:28 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:28:28 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:29:21 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:29:21 - src.core.detector - INFO - 开始检测APP: 青藤壁纸
2025-07-28 17:29:21 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:29:21 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:29:21 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:29:49 - privacy_detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:29:49 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:29:49 - src.core.detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:29:49 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:29:49 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:29:49 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:29:49 - privacy_detector - ERROR - 检测失败: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:30:22 - privacy_detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:30:22 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:30:22 - src.core.detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:30:22 - src.core.detector - ERROR - 检测过程中发生错误: 设备连接失败: device not found
2025-07-28 17:30:22 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:30:22 - privacy_detector - ERROR - 检测失败: 设备连接失败: device not found
2025-07-28 17:30:32 - privacy_detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:30:32 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:30:32 - src.core.detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:30:32 - src.core.detector - INFO - 发现设备: [('Local System', 'local'), ('Local Socket', 'remote'), ('GDB Remote Stub', 'remote')]
2025-07-28 17:30:32 - src.core.detector - INFO - 已连接到设备: GDB Remote Stub (remote)
2025-07-28 17:30:32 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: the specified GDB remote stub cannot be reached: Could not connect to 127.0.0.1: Connection refused
2025-07-28 17:30:32 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:30:32 - privacy_detector - ERROR - 检测失败: APP附加失败: the specified GDB remote stub cannot be reached: Could not connect to 127.0.0.1: Connection refused
2025-07-28 17:31:01 - privacy_detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:31:01 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:31:01 - src.core.detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:31:01 - src.core.detector - INFO - 发现设备: [('Local System', 'local', 'local'), ('Local Socket', 'remote', 'socket'), ('GDB Remote Stub', 'remote', 'barebone'), ('V2241HA', 'usb', '127.0.0.1:16416')]
2025-07-28 17:31:01 - src.core.detector - INFO - 选择USB设备: V2241HA
2025-07-28 17:31:01 - src.core.detector - INFO - 已连接到设备: V2241HA (usb)
2025-07-28 17:31:01 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server: closed
2025-07-28 17:31:01 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:31:01 - privacy_detector - ERROR - 检测失败: APP附加失败: unable to connect to remote frida-server: closed
2025-07-28 17:31:51 - privacy_detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:31:51 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:31:51 - src.core.detector - INFO - 开始检测APP: com.tencent.android.qqdownloader
2025-07-28 17:31:51 - src.core.detector - INFO - 发现设备: [('Local System', 'local', 'local'), ('Local Socket', 'remote', 'socket'), ('GDB Remote Stub', 'remote', 'barebone'), ('V2241HA', 'usb', '127.0.0.1:16416')]
2025-07-28 17:31:51 - src.core.detector - INFO - 选择USB设备: V2241HA
2025-07-28 17:31:51 - src.core.detector - INFO - 已连接到设备: V2241HA (usb)
2025-07-28 17:31:51 - src.core.detector - INFO - 发现运行中的进程: com.tencent.android.qqdownloader (PID: 3273)
2025-07-28 17:31:51 - src.core.detector - WARNING - 附加尝试 1 失败: unable to connect to remote frida-server: closed
2025-07-28 17:31:52 - src.core.detector - WARNING - 附加尝试 2 失败: unable to connect to remote frida-server: closed
2025-07-28 17:31:53 - src.core.detector - WARNING - 附加尝试 3 失败: unable to connect to remote frida-server: closed
2025-07-28 17:31:53 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server: closed
2025-07-28 17:31:53 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:31:53 - privacy_detector - ERROR - 检测失败: APP附加失败: unable to connect to remote frida-server: closed
2025-07-28 17:32:08 - privacy_detector - INFO - 开始检测APP: com.android.packageinstaller
2025-07-28 17:32:08 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:32:08 - src.core.detector - INFO - 开始检测APP: com.android.packageinstaller
2025-07-28 17:32:08 - src.core.detector - INFO - 发现设备: [('Local System', 'local', 'local'), ('Local Socket', 'remote', 'socket'), ('GDB Remote Stub', 'remote', 'barebone'), ('V2241HA', 'usb', '127.0.0.1:16416')]
2025-07-28 17:32:08 - src.core.detector - INFO - 选择USB设备: V2241HA
2025-07-28 17:32:08 - src.core.detector - INFO - 已连接到设备: V2241HA (usb)
2025-07-28 17:32:08 - src.core.detector - INFO - 发现运行中的进程: com.android.packageinstaller (PID: 3023)
2025-07-28 17:32:08 - src.core.detector - INFO - 已附加到进程: 3023
2025-07-28 17:32:08 - src.core.detector - INFO - APP信息收集完成: {'package_name': 'com.android.packageinstaller', 'device_info': 'V2241HA (usb)', 'detection_start_time': 1753695128.81174, 'app_name': 'packageinstaller', 'version': 'unknown'}
2025-07-28 17:32:09 - src.core.detector - INFO - Frida脚本加载成功
2025-07-28 17:32:09 - src.core.detector - INFO - 开始执行检测阶段
2025-07-28 17:32:14 - src.detectors.privacy_policy_detector - INFO - 开始隐私政策合规检测
2025-07-28 17:32:14 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP中未发现隐私政策
2025-07-28 17:32:19 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP首次运行时未通过弹窗等明显方式提示用户阅读隐私政策
2025-07-28 17:32:19 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 访问隐私政策需要超过4次点击（实际需要5次）
2025-07-28 17:32:19 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 缺少隐私政策变更通知机制
2025-07-28 17:32:19 - src.detectors.privacy_policy_detector - INFO - 隐私政策检测完成，发现 4 个违规项
2025-07-28 17:32:19 - src.core.detector - INFO - 隐私政策检测完成，发现 4 个违规项
2025-07-28 17:32:19 - src.detectors.permission_detector - INFO - 开始权限和数据收集合规检测
2025-07-28 17:32:19 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 利用算法定向推送时未提供关闭个性化推荐的选项
2025-07-28 17:32:19 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 未在APP中提供便捷的撤回同意路径
2025-07-28 17:32:19 - src.detectors.permission_detector - INFO - 权限和数据收集检测完成，发现 2 个违规项
2025-07-28 17:32:19 - src.core.detector - INFO - 权限检测完成，发现 2 个违规项
2025-07-28 17:32:19 - src.detectors.sdk_detector - INFO - 开始第三方SDK合规检测
2025-07-28 17:32:19 - src.detectors.sdk_detector - INFO - 第三方SDK检测完成，发现 0 个违规项
2025-07-28 17:32:19 - src.core.detector - INFO - SDK检测完成，发现 0 个违规项
2025-07-28 17:32:19 - src.detectors.user_rights_detector - INFO - 开始用户权利保障合规检测
2025-07-28 17:32:19 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: APP内缺少用户权利功能: 修改资料, 删除数据, 注销账号
2025-07-28 17:32:19 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: 未建立投诉渠道或投诉处理超期
2025-07-28 17:32:19 - src.detectors.user_rights_detector - INFO - 用户权利保障检测完成，发现 2 个违规项
2025-07-28 17:32:19 - src.core.detector - INFO - 用户权利检测完成，发现 2 个违规项
2025-07-28 17:32:49 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 17:32:49 - src.utils.report_generator - INFO - 报告已生成: ./test_reports/privacy_compliance_report_com.android.packageinstaller_20250728_173249.xlsx
2025-07-28 17:32:49 - src.core.detector - INFO - 检测报告已生成: ./test_reports/privacy_compliance_report_com.android.packageinstaller_20250728_173249.xlsx
2025-07-28 17:32:49 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:32:49 - src.core.detector - INFO - 检测完成，耗时: 40.3秒
2025-07-28 17:32:49 - privacy_detector - INFO - 检测成功完成: {'success': True, 'report_path': './test_reports/privacy_compliance_report_com.android.packageinstaller_20250728_173249.xlsx', 'detection_duration': 40.25203824043274, 'violations_count': 8, 'app_info': {'package_name': 'com.android.packageinstaller', 'device_info': 'V2241HA (usb)', 'detection_start_time': 1753695128.81174, 'app_name': 'packageinstaller', 'version': 'unknown', 'detection_duration': 40.20192503929138}}
2025-07-28 17:34:44 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:34:45 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:34:45 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:34:45 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:34:45 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:34:52 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:34:52 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:34:52 - src.core.detector - INFO - 已连接到设备: Local Socket
2025-07-28 17:34:52 - src.core.detector - ERROR - 检测过程中发生错误: APP附加失败: unable to connect to remote frida-server
2025-07-28 17:34:52 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:36:05 - privacy_detector - INFO - 启动GUI界面
2025-07-28 17:36:19 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:36:19 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:36:19 - src.core.detector - INFO - 发现设备: [('Local System', 'local', 'local'), ('Local Socket', 'remote', 'socket'), ('GDB Remote Stub', 'remote', 'barebone'), ('V2241HA', 'usb', '127.0.0.1:16416')]
2025-07-28 17:36:19 - src.core.detector - INFO - 选择USB设备: V2241HA
2025-07-28 17:36:19 - src.core.detector - INFO - 已连接到设备: V2241HA (usb)
2025-07-28 17:36:19 - src.core.detector - INFO - APP未运行，尝试启动: com.ivy.qtbz
2025-07-28 17:36:19 - src.core.detector - INFO - 已启动并附加到APP: com.ivy.qtbz (PID: 4915)
2025-07-28 17:36:19 - src.core.detector - INFO - APP信息收集完成: {'package_name': 'com.ivy.qtbz', 'device_info': 'V2241HA (usb)', 'detection_start_time': 1753695379.7287848, 'app_name': 'qtbz', 'version': 'unknown'}
2025-07-28 17:36:19 - src.core.detector - INFO - Frida脚本加载成功
2025-07-28 17:36:19 - src.core.detector - INFO - 开始执行检测阶段
2025-07-28 17:36:24 - src.detectors.privacy_policy_detector - INFO - 开始隐私政策合规检测
2025-07-28 17:36:24 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP中未发现隐私政策
2025-07-28 17:36:29 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP首次运行时未通过弹窗等明显方式提示用户阅读隐私政策
2025-07-28 17:36:29 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 访问隐私政策需要超过4次点击（实际需要5次）
2025-07-28 17:36:29 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 缺少隐私政策变更通知机制
2025-07-28 17:36:29 - src.detectors.privacy_policy_detector - INFO - 隐私政策检测完成，发现 4 个违规项
2025-07-28 17:36:29 - src.core.detector - INFO - 隐私政策检测完成，发现 4 个违规项
2025-07-28 17:36:29 - src.detectors.permission_detector - INFO - 开始权限和数据收集合规检测
2025-07-28 17:36:30 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 利用算法定向推送时未提供关闭个性化推荐的选项
2025-07-28 17:36:30 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 未在APP中提供便捷的撤回同意路径
2025-07-28 17:36:30 - src.detectors.permission_detector - INFO - 权限和数据收集检测完成，发现 2 个违规项
2025-07-28 17:36:30 - src.core.detector - INFO - 权限检测完成，发现 2 个违规项
2025-07-28 17:36:30 - src.detectors.sdk_detector - INFO - 开始第三方SDK合规检测
2025-07-28 17:36:30 - src.detectors.sdk_detector - INFO - 第三方SDK检测完成，发现 0 个违规项
2025-07-28 17:36:30 - src.core.detector - INFO - SDK检测完成，发现 0 个违规项
2025-07-28 17:36:30 - src.detectors.user_rights_detector - INFO - 开始用户权利保障合规检测
2025-07-28 17:36:30 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: APP内缺少用户权利功能: 修改资料, 删除数据, 注销账号
2025-07-28 17:36:30 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: 未建立投诉渠道或投诉处理超期
2025-07-28 17:36:30 - src.detectors.user_rights_detector - INFO - 用户权利保障检测完成，发现 2 个违规项
2025-07-28 17:36:30 - src.core.detector - INFO - 用户权利检测完成，发现 2 个违规项
2025-07-28 17:37:00 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 17:37:00 - src.utils.report_generator - INFO - 报告已生成: ./reports/privacy_compliance_report_com.ivy.qtbz_20250728_173700.xlsx
2025-07-28 17:37:00 - src.core.detector - INFO - 检测报告已生成: ./reports/privacy_compliance_report_com.ivy.qtbz_20250728_173700.xlsx
2025-07-28 17:37:00 - src.core.detector - INFO - 资源清理完成
2025-07-28 17:37:00 - src.core.detector - INFO - 检测完成，耗时: 40.5秒
2025-07-28 17:57:23 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 17:57:23 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 17:57:23 - src.core.detector - INFO - 发现设备: [('Local System', 'local', 'local'), ('Local Socket', 'remote', 'socket'), ('GDB Remote Stub', 'remote', 'barebone')]
2025-07-28 17:57:23 - src.core.detector - ERROR - 检测过程中发生错误: 设备连接失败: 未找到可用的设备，请确保设备已连接并启动Frida服务端
2025-07-28 17:57:23 - src.core.detector - INFO - 资源清理完成
2025-07-28 18:09:27 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:09:27 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 18:09:28 - src.core.detector - INFO - 发现设备: [('Local System', 'local', 'local'), ('Local Socket', 'remote', 'socket'), ('GDB Remote Stub', 'remote', 'barebone')]
2025-07-28 18:09:28 - src.core.detector - ERROR - 检测过程中发生错误: 设备连接失败: 未找到可用的设备，请确保设备已连接并启动Frida服务端
2025-07-28 18:09:28 - src.core.detector - INFO - 资源清理完成
2025-07-28 18:09:48 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 18:09:48 - src.utils.report_generator - INFO - 报告已生成: ./test_reports/privacy_compliance_report_com.performance.test_20250728_180948.xlsx
2025-07-28 18:09:48 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:09:48 - test_logger - INFO - Info message
2025-07-28 18:09:48 - test_logger - WARNING - Warning message
2025-07-28 18:09:48 - test_logger - ERROR - Error message
2025-07-28 18:09:48 - src.detectors.permission_detector - INFO - 开始权限和数据收集合规检测
2025-07-28 18:09:48 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 在用户同意隐私政策前收集个人信息或申请权限
2025-07-28 18:09:48 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 收集device_id信息超出用户授权范围
2025-07-28 18:09:48 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 利用算法定向推送时未提供关闭个性化推荐的选项
2025-07-28 18:09:48 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 未在APP中提供便捷的撤回同意路径
2025-07-28 18:09:48 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 收集的信息与现有功能无关
2025-07-28 18:09:48 - src.detectors.permission_detector - INFO - 权限和数据收集检测完成，发现 5 个违规项
2025-07-28 18:09:48 - src.detectors.privacy_policy_detector - INFO - 开始隐私政策合规检测
2025-07-28 18:09:48 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP中未发现隐私政策
2025-07-28 18:09:53 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP首次运行时未通过弹窗等明显方式提示用户阅读隐私政策
2025-07-28 18:09:53 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 访问隐私政策需要超过4次点击（实际需要5次）
2025-07-28 18:09:53 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 缺少隐私政策变更通知机制
2025-07-28 18:09:53 - src.detectors.privacy_policy_detector - INFO - 隐私政策检测完成，发现 4 个违规项
2025-07-28 18:09:53 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 18:09:53 - src.utils.report_generator - INFO - 报告已生成: ./test_reports/privacy_compliance_report_com.test.app_20250728_180953.xlsx
2025-07-28 18:09:53 - src.detectors.sdk_detector - INFO - 开始第三方SDK合规检测
2025-07-28 18:09:53 - src.detectors.sdk_detector - WARNING - 发现第三方SDK违规: 未匿名化且未经用户同意，通过SDK等向第三方传输个人信息
2025-07-28 18:09:53 - src.detectors.sdk_detector - INFO - 第三方SDK检测完成，发现 1 个违规项
2025-07-28 18:09:53 - src.detectors.user_rights_detector - INFO - 开始用户权利保障合规检测
2025-07-28 18:09:53 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: APP内缺少用户权利功能: 修改资料, 删除数据, 注销账号
2025-07-28 18:09:53 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: 未建立投诉渠道或投诉处理超期
2025-07-28 18:09:53 - src.detectors.user_rights_detector - INFO - 用户权利保障检测完成，发现 2 个违规项
2025-07-28 18:22:53 - privacy_detector - INFO - 启动GUI界面
2025-07-28 18:28:14 - privacy_detector - INFO - 启动GUI界面
2025-07-28 18:29:01 - privacy_detector - INFO - 启动GUI界面
2025-07-28 18:32:26 - privacy_detector - INFO - 启动GUI界面
2025-07-28 18:33:20 - privacy_detector - INFO - 启动GUI界面
2025-07-28 18:35:11 - privacy_detector - INFO - 启动GUI界面
2025-07-28 18:37:07 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:37:07 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 18:37:07 - src.core.detector - INFO - 发现设备: [('Local System', 'local', 'local'), ('Local Socket', 'remote', 'socket'), ('GDB Remote Stub', 'remote', 'barebone'), ('V2241HA', 'usb', '127.0.0.1:16416')]
2025-07-28 18:37:07 - src.core.detector - INFO - 选择USB设备: V2241HA
2025-07-28 18:37:07 - src.core.detector - INFO - 已连接到设备: V2241HA (usb)
2025-07-28 18:37:07 - src.core.detector - INFO - APP未运行，尝试启动: com.ivy.qtbz
2025-07-28 18:37:07 - src.core.detector - INFO - 已启动并附加到APP: com.ivy.qtbz (PID: 3794)
2025-07-28 18:37:07 - src.core.detector - INFO - APP信息收集完成: {'package_name': 'com.ivy.qtbz', 'device_info': 'V2241HA (usb)', 'detection_start_time': 1753699027.280231, 'app_name': 'qtbz', 'version': 'unknown'}
2025-07-28 18:37:07 - src.core.detector - INFO - Frida脚本加载成功
2025-07-28 18:37:07 - src.core.detector - INFO - 开始执行交互式检测阶段
2025-07-28 18:37:07 - src.core.interactive_detector - INFO - 开始交互式隐私合规检测
2025-07-28 18:37:07 - src.core.interactive_detector - INFO - [隐私政策检测] 10% - 检测APP启动时的隐私政策显示...
2025-07-28 18:37:10 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:37:10 - src.core.interactive_detector - ERROR - 交互式检测过程中发生错误: InteractivePrivacyDetector._add_violation() missing 3 required positional arguments: 'evidence', 'severity', and 'compliance_item'
2025-07-28 18:37:10 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 18:37:10 - src.utils.report_generator - INFO - 报告已生成: ./reports/privacy_compliance_report_com.ivy.qtbz_20250728_183710.xlsx
2025-07-28 18:37:10 - src.core.detector - INFO - 检测报告已生成: ./reports/privacy_compliance_report_com.ivy.qtbz_20250728_183710.xlsx
2025-07-28 18:37:10 - src.core.detector - INFO - 资源清理完成
2025-07-28 18:37:10 - src.core.detector - INFO - 检测完成，耗时: 3.4秒
2025-07-28 18:37:27 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:37:27 - src.core.detector - INFO - 开始检测APP: com.ivy.qtbz
2025-07-28 18:37:27 - src.core.detector - INFO - 发现设备: [('Local System', 'local', 'local'), ('Local Socket', 'remote', 'socket'), ('GDB Remote Stub', 'remote', 'barebone'), ('V2241HA', 'usb', '127.0.0.1:16416')]
2025-07-28 18:37:27 - src.core.detector - INFO - 选择USB设备: V2241HA
2025-07-28 18:37:27 - src.core.detector - INFO - 已连接到设备: V2241HA (usb)
2025-07-28 18:37:27 - src.core.detector - INFO - APP未运行，尝试启动: com.ivy.qtbz
2025-07-28 18:37:27 - src.core.detector - INFO - 已启动并附加到APP: com.ivy.qtbz (PID: 3881)
2025-07-28 18:37:27 - src.core.detector - INFO - APP信息收集完成: {'package_name': 'com.ivy.qtbz', 'device_info': 'V2241HA (usb)', 'detection_start_time': 1753699047.225983, 'app_name': 'qtbz', 'version': 'unknown'}
2025-07-28 18:37:27 - src.core.detector - INFO - Frida脚本加载成功
2025-07-28 18:37:27 - src.core.detector - INFO - 开始执行交互式检测阶段
2025-07-28 18:37:27 - src.core.interactive_detector - INFO - 开始交互式隐私合规检测
2025-07-28 18:37:27 - src.core.interactive_detector - INFO - [隐私政策检测] 10% - 检测APP启动时的隐私政策显示...
2025-07-28 18:37:30 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:37:30 - src.core.interactive_detector - ERROR - 交互式检测过程中发生错误: InteractivePrivacyDetector._add_violation() missing 3 required positional arguments: 'evidence', 'severity', and 'compliance_item'
2025-07-28 18:37:30 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 18:37:30 - src.utils.report_generator - INFO - 报告已生成: ./reports/privacy_compliance_report_com.ivy.qtbz_20250728_183730.xlsx
2025-07-28 18:37:30 - src.core.detector - INFO - 检测报告已生成: ./reports/privacy_compliance_report_com.ivy.qtbz_20250728_183730.xlsx
2025-07-28 18:37:30 - src.core.detector - INFO - 资源清理完成
2025-07-28 18:37:30 - src.core.detector - INFO - 检测完成，耗时: 3.5秒
2025-07-28 18:42:36 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 18:42:36 - src.utils.report_generator - INFO - 报告已生成: ./test_reports/privacy_compliance_report_com.performance.test_20250728_184236.xlsx
2025-07-28 18:42:36 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:42:36 - test_logger - INFO - Info message
2025-07-28 18:42:36 - test_logger - WARNING - Warning message
2025-07-28 18:42:36 - test_logger - ERROR - Error message
2025-07-28 18:42:36 - src.detectors.permission_detector - INFO - 开始权限和数据收集合规检测
2025-07-28 18:42:36 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 在用户同意隐私政策前收集个人信息或申请权限
2025-07-28 18:42:36 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 收集device_id信息超出用户授权范围
2025-07-28 18:42:36 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 利用算法定向推送时未提供关闭个性化推荐的选项
2025-07-28 18:42:36 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 未在APP中提供便捷的撤回同意路径
2025-07-28 18:42:36 - src.detectors.permission_detector - WARNING - 发现权限数据违规: 收集的信息与现有功能无关
2025-07-28 18:42:36 - src.detectors.permission_detector - INFO - 权限和数据收集检测完成，发现 5 个违规项
2025-07-28 18:42:36 - src.detectors.privacy_policy_detector - INFO - 开始隐私政策合规检测
2025-07-28 18:42:37 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:42:37 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP中未发现隐私政策
2025-07-28 18:42:40 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:42:40 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: APP首次运行时未通过弹窗等明显方式提示用户阅读隐私政策
2025-07-28 18:42:40 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 访问隐私政策需要超过4次点击（实际需要5次）
2025-07-28 18:42:40 - src.detectors.privacy_policy_detector - WARNING - 发现隐私政策违规: 缺少隐私政策变更通知机制
2025-07-28 18:42:40 - src.detectors.privacy_policy_detector - INFO - 隐私政策检测完成，发现 4 个违规项
2025-07-28 18:42:40 - src.utils.report_generator - INFO - 开始生成隐私合规检测报告
2025-07-28 18:42:40 - src.utils.report_generator - INFO - 报告已生成: ./test_reports/privacy_compliance_report_com.test.app_20250728_184240.xlsx
2025-07-28 18:42:40 - src.detectors.sdk_detector - INFO - 开始第三方SDK合规检测
2025-07-28 18:42:40 - src.detectors.sdk_detector - WARNING - 发现第三方SDK违规: 未匿名化且未经用户同意，通过SDK等向第三方传输个人信息
2025-07-28 18:42:40 - src.detectors.sdk_detector - INFO - 第三方SDK检测完成，发现 1 个违规项
2025-07-28 18:42:40 - src.detectors.user_rights_detector - INFO - 开始用户权利保障合规检测
2025-07-28 18:42:40 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: APP内缺少用户权利功能: 修改资料, 删除数据, 注销账号
2025-07-28 18:42:40 - src.detectors.user_rights_detector - WARNING - 发现用户权利违规: 未建立投诉渠道或投诉处理超期
2025-07-28 18:42:40 - src.detectors.user_rights_detector - INFO - 用户权利保障检测完成，发现 2 个违规项
2025-07-28 18:43:14 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:44:39 - privacy_detector - INFO - 启动GUI界面
2025-07-28 18:45:09 - privacy_detector - INFO - 启动GUI界面
2025-07-28 18:45:13 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:45:20 - src.utils.ui_analyzer - WARNING - UI dump命令失败: error: closed

2025-07-28 18:45:21 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:45:22 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:45:23 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:45:24 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:45:25 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:45:26 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:45:27 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:45:28 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:45:30 - src.utils.ui_analyzer - WARNING - UI dump命令失败: adb: device offline

2025-07-28 18:46:17 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:46:20 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:21 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:23 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:24 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:25 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:26 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:27 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:28 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:29 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:30 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:55 - src.core.detector - INFO - 配置文件加载成功: ./config/config.json
2025-07-28 18:46:58 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:46:59 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:47:00 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:47:01 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:47:02 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:47:03 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:47:04 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:47:05 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:47:06 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:47:07 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:50:44 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:52:45 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:52:46 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:52:47 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:52:48 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:52:49 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:52:50 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

2025-07-28 18:52:51 - src.utils.ui_analyzer - WARNING - UI dump命令失败: /system/bin/sh: uiautomator: inaccessible or not found

