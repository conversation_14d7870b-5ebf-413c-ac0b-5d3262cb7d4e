#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持续交互式检测功能
验证真正的持续监控和违规检测
"""

import sys
import os
import time

# 添加路径
sys.path.insert(0, '.')
sys.path.insert(0, 'src')

def test_continuous_interactive_detection():
    """测试持续交互式检测"""
    print("🚀 测试持续交互式检测功能")
    print("=" * 60)
    
    try:
        from src.gui.main_window import ImprovedPrivacyDetector
        
        # 创建检测器
        detector = ImprovedPrivacyDetector(
            package_name="com.ivy.qtbz",
            device_id=None,
            interactive_mode=True,
            output_dir="./test_reports"
        )
        
        print("✅ 检测器初始化成功")
        print(f"📱 目标APP: {detector.package_name}")
        print(f"🔧 交互模式: {detector.interactive_mode}")
        
        # 设置回调函数
        def status_callback(message):
            print(f"[状态] {message}")
        
        def violation_callback(violation):
            severity = violation.get('severity', 'unknown')
            description = violation.get('description', 'unknown')
            is_violation = violation.get('isViolation', False)
            
            if is_violation:
                print(f"🚨 [违规] {severity.upper()}: {description}")
            else:
                print(f"📊 [记录] {description}")
        
        detector.on_status_update = status_callback
        detector.on_violation_found = violation_callback
        
        print("\n🎯 开始持续交互式检测...")
        print("📋 请按照弹窗提示进行操作")
        print("⚠️ 这是真正的持续检测，会一直运行直到您停止")
        
        # 运行检测
        result = detector.run_detection()
        
        print("\n📊 检测结果:")
        print("=" * 40)
        
        if result.get('success'):
            print(f"✅ 检测成功完成")
            print(f"⏱️ 检测时长: {result.get('detection_duration', 0):.1f} 秒")
            print(f"🚨 发现违规: {result.get('violations_count', 0)} 个")
            print(f"📄 报告路径: {result.get('report_path', 'N/A')}")
            
            summary = result.get('summary', {})
            print(f"\n📈 详细统计:")
            print(f"   - 高危违规: {summary.get('high_severity_violations', 0)} 个")
            print(f"   - 中危违规: {summary.get('medium_severity_violations', 0)} 个")
            print(f"   - 信息记录: {summary.get('info_records', 0)} 个")
            
        elif result.get('skipped'):
            print(f"⏭️ 检测被跳过: {result.get('reason', '未知原因')}")
        else:
            print(f"❌ 检测失败: {result.get('error', '未知错误')}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ 检测被用户中断")
        if 'detector' in locals():
            detector.stop_detection()
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成")
    print("=" * 40)
    
    try:
        from src.gui.main_window import PrivacyDetectorGUI
        
        print("✅ GUI模块导入成功")
        
        # 创建GUI实例（不显示窗口）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        gui = PrivacyDetectorGUI(root)
        
        print("✅ GUI初始化成功")
        print("📋 GUI包含以下功能:")
        print("   - 持续交互式检测")
        print("   - 实时状态更新")
        print("   - 违规行为警告")
        print("   - 详细报告生成")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "=" * 60)
    print("📖 持续交互式检测使用指南")
    print("=" * 60)
    
    print("""
🎯 功能特点:
• 真正的持续监控，不会过早结束
• 基于用户真实操作的差异化检测
• 实时Frida监控权限API调用
• 详细的违规证据和调用堆栈
• 智能的隐私政策内容分析

🔄 检测流程:
1. 启动APP并等待隐私政策弹窗
2. 实时分析隐私政策内容质量
3. 用户选择同意/不同意
4. 启动Frida监控脚本
5. 持续监控直到用户停止

📊 监控内容:
• 位置权限 (LocationManager)
• 电话权限 (TelephonyManager) 
• 联系人权限 (ContactsContract)
• 设备标识 (getDeviceId)
• 电话号码 (getLine1Number)

🚨 违规检测:
• 用户拒绝后的违规API调用
• 详细的调用堆栈信息
• 第三方SDK违规行为
• 实时违规警告

🎮 使用方法:
1. 运行: python run.py
2. 选择: 1. 🖥️ 启动GUI界面
3. 模式: 选择"改进模式（推荐）"
4. 开始: 点击"开始检测"
5. 操作: 按照弹窗提示进行真实操作
6. 停止: 点击"停止检测"结束监控

💡 注意事项:
• 确保Android设备已连接
• 确保USB调试已启用
• 建议安装Frida服务端
• 检测过程中请真实操作APP
• 可随时停止检测
""")

def main():
    """主函数"""
    print("🧪 持续交互式检测功能测试")
    print("=" * 60)
    
    print("此测试将验证新的持续交互式检测系统")
    print("解决了以下问题:")
    print("• ❌ 检测过早结束 → ✅ 持续监控直到用户停止")
    print("• ❌ 误报严重 → ✅ 基于真实用户操作")
    print("• ❌ 缺乏真正交互 → ✅ 等待用户选择并差异化检测")
    print("• ❌ 违规检测不准确 → ✅ Frida实时监控API调用")
    print()
    
    # 询问用户要进行哪种测试
    print("请选择测试类型:")
    print("1. 完整功能测试（推荐）")
    print("2. GUI集成测试")
    print("3. 显示使用指南")
    print("4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            print("\n🚀 开始完整功能测试...")
            success = test_continuous_interactive_detection()
            
            if success:
                print("\n🎉 测试完成！")
                print("现在您可以使用 python run.py 启动GUI进行真实检测")
            else:
                print("\n⚠️ 测试未完成")
                
        elif choice == '2':
            print("\n🖥️ 开始GUI集成测试...")
            success = test_gui_integration()
            
            if success:
                print("\n✅ GUI集成正常！")
            else:
                print("\n❌ GUI集成有问题")
                
        elif choice == '3':
            show_usage_guide()
            
        elif choice == '4':
            print("👋 退出测试")
            
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
