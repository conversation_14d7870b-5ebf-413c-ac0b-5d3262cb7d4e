# APP隐私合规检测工具

基于Frida的Android APP隐私合规自动化检测工具，支持30项隐私合规检测项目，能够生成详细的Excel报告。

## 功能特性

### 🔍 全面的检测覆盖
- **30项隐私合规检测**：覆盖《个人信息保护法》的主要合规要求
- **动态行为分析**：基于Frida的实时Hook技术
- **多维度检测**：隐私政策、权限申请、数据收集、第三方SDK、用户权利

### 📊 详细的报告生成
- **Excel格式报告**：包含违规详情、时间线、整改建议
- **可视化统计**：违规分布、严重程度分析
- **证据链完整**：调用栈、时间戳、具体证据

### 🖥️ 友好的用户界面
- **GUI界面**：简单易用的图形界面
- **命令行支持**：支持自动化集成
- **实时日志**：检测过程可视化

## 检测项目

### 隐私政策相关 (1-8)
1. 无隐私政策或规则缺失
2. 首次运行未提示隐私政策
3. 隐私政策访问路径过深
4. 隐私政策难以阅读
5. 未逐项说明收集信息
6. 变更规则未通知用户
7. 敏感权限未同步告知目的
8. 规则内容晦涩难懂

### 权限和数据收集 (9-23)
9. 提前收集信息或权限
10. 拒绝后继续收集或频繁骚扰
11. 超范围收集信息或权限
12. 非明示同意（默认勾选）
13. 擅自恢复默认权限
14. 未提供非定向推送选项
15. 欺诈诱骗同意
16. 未提供撤回同意渠道
17. 违反声明的收集规则
18. 收集与业务无关的信息
19. 拒绝非必要权限即禁用功能
20. 新增功能超原有同意范围
21. 超频次收集信息
22. 以"改善体验"强制收集
23. 一次性索要多个权限

### 第三方数据共享 (24-25)
24. 未经同意向第三方提供信息
25. 第三方应用共享未授权

### 用户权利保障 (26-30)
26. 未提供更正/删除/注销功能
27. 设置注销/更正不合理条件
28. 未及时响应用户操作
29. 前端操作未同步后台
30. 未建立投诉渠道或超期处理

## 安装要求

### 系统要求
- Python 3.7+
- Android设备或模拟器
- ADB调试环境

### 依赖安装
```bash
pip install -r requirements.txt
```

### Frida环境配置
1. 安装Frida服务端到Android设备
2. 确保ADB连接正常
3. 启用USB调试

## 使用方法

### GUI界面使用
```bash
python main.py --gui
```

1. 输入目标APP包名
2. 选择设备（可选）
3. 设置输出目录
4. 点击"开始检测"
5. 等待检测完成
6. 查看生成的Excel报告

### 命令行使用
```bash
# 基本检测
python main.py --package com.example.app

# 指定设备和输出目录
python main.py --package com.example.app --device device_id --output ./reports

# 使用自定义配置
python main.py --package com.example.app --config ./config/custom_config.json
```

## 配置说明

### 配置文件 (config/config.json)
```json
{
  "detection_settings": {
    "timeout": 300,
    "max_violations_per_type": 100,
    "enable_screenshot": true,
    "enable_network_monitoring": true,
    "enable_file_monitoring": true
  },
  "frida_settings": {
    "spawn_timeout": 10,
    "attach_timeout": 5,
    "script_timeout": 30
  },
  "report_settings": {
    "format": "xlsx",
    "include_screenshots": true,
    "include_call_stack": true,
    "include_timeline": true,
    "language": "zh-CN"
  }
}
```

## 检测原理

### 动态Hook技术
- **API监控**：Hook Android系统API调用
- **权限拦截**：监控权限申请和使用
- **网络分析**：检测数据传输行为
- **UI分析**：监控用户界面交互

### 违规判定逻辑
- **时序分析**：基于用户操作时间线判定
- **行为关联**：关联权限申请与数据收集
- **政策比对**：对比隐私政策与实际行为
- **第三方识别**：识别SDK和第三方数据传输

## 报告说明

### Excel报告结构
1. **检测摘要**：基本信息和统计结果
2. **违规详情**：详细的违规项目列表
3. **检测时间线**：按时间顺序的事件记录
4. **详细数据**：原始检测数据
5. **整改建议**：针对性的合规建议

### 违规严重程度
- **高风险**：严重违反法规，需立即整改
- **中风险**：存在合规风险，建议整改
- **低风险**：轻微问题，可优化改进

## 注意事项

### 使用限制
- 需要Root权限或可调试的APP
- 部分加固APP可能检测受限
- 网络环境可能影响检测结果

### 法律声明
- 本工具仅用于合规检测和安全研究
- 使用者需遵守相关法律法规
- 不得用于恶意攻击或非法用途

### 技术支持
- 检测准确率持续优化中
- 支持自定义检测规则
- 欢迎反馈问题和建议

## 开发说明

### 项目结构
```
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖列表
├── config/                 # 配置文件
├── src/                    # 源代码
│   ├── core/              # 核心检测模块
│   ├── detectors/         # 各类检测器
│   ├── frida_scripts/     # Frida Hook脚本
│   ├── gui/               # GUI界面
│   └── utils/             # 工具类
├── logs/                  # 日志文件
└── reports/               # 检测报告
```

### 扩展开发
- 添加新的检测项目
- 自定义报告格式
- 集成其他分析工具
- 支持更多平台

## 更新日志

### v1.0.0
- 实现30项隐私合规检测
- 支持GUI和命令行界面
- 生成详细Excel报告
- 基于Frida的动态分析

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进本项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者
