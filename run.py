#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
提供便捷的启动选项和环境检查
"""

import os
import sys
import subprocess
import time

def print_menu():
    """显示主菜单"""
    menu = """
╔══════════════════════════════════════════════════════════════╗
║                APP隐私合规检测工具 - 快速启动                  ║
╚══════════════════════════════════════════════════════════════╝

请选择操作:

1. 🖥️  启动GUI界面
2. 📱 检测指定APP (命令行)
3. 🧪 运行系统测试
4. 🔧 环境检查
5. 📚 查看使用帮助
6. 🚀 安装/更新环境
7. ❌ 退出

"""
    print(menu)

def check_quick_environment():
    """快速环境检查"""
    print("🔍 快速环境检查...")
    
    issues = []
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        issues.append("Python版本过低 (需要3.7+)")
    
    # 检查关键依赖
    try:
        import frida
        print("✅ Frida已安装")
    except ImportError:
        issues.append("Frida未安装")
    
    try:
        import openpyxl
        print("✅ openpyxl已安装")
    except ImportError:
        issues.append("openpyxl未安装")
    
    # 检查ADB
    try:
        result = subprocess.run(["adb", "version"], capture_output=True)
        if result.returncode == 0:
            print("✅ ADB环境正常")
        else:
            issues.append("ADB命令执行失败")
    except FileNotFoundError:
        issues.append("ADB未安装")
    
    # 检查设备连接
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
        devices = [line for line in result.stdout.split('\n') if '\tdevice' in line]
        if devices:
            print(f"✅ 检测到 {len(devices)} 个设备")
        else:
            issues.append("未检测到Android设备")
    except:
        pass
    
    if issues:
        print("\n⚠️  发现以下问题:")
        for issue in issues:
            print(f"   - {issue}")
        print("\n建议运行选项6进行环境配置")
        return False
    else:
        print("\n✅ 环境检查通过")
        return True

def launch_gui():
    """启动GUI界面"""
    print("🖥️  启动GUI界面...")
    try:
        subprocess.run([sys.executable, "main.py", "--gui"])
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")

def detect_app():
    """检测指定APP"""
    print("📱 APP隐私合规检测")
    print("-" * 30)
    
    # 获取用户输入
    package_name = input("请输入APP包名 (例如: com.example.app): ").strip()
    if not package_name:
        print("❌ 包名不能为空")
        return
    
    device_id = input("设备ID (留空自动检测): ").strip()
    output_dir = input("输出目录 (留空使用默认): ").strip()
    
    # 构建命令
    cmd = [sys.executable, "main.py", "--package", package_name]
    
    if device_id:
        cmd.extend(["--device", device_id])
    
    if output_dir:
        cmd.extend(["--output", output_dir])
    
    print(f"\n🚀 开始检测: {package_name}")
    print("按 Ctrl+C 可中断检测")
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n⚠️  检测被用户中断")
    except Exception as e:
        print(f"❌ 检测失败: {e}")

def run_tests():
    """运行系统测试"""
    print("🧪 运行系统测试...")
    try:
        subprocess.run([sys.executable, "test_system.py"])
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
📚 使用帮助

=== 基本使用 ===
1. 确保Android设备已连接并启用USB调试
2. 在设备上安装并启动Frida服务端
3. 选择GUI界面或命令行模式进行检测

=== 环境要求 ===
- Python 3.7+
- Android设备或模拟器
- ADB工具
- Frida环境

=== 常见问题 ===
Q: 检测失败怎么办？
A: 1. 检查设备连接
   2. 确认Frida服务端运行
   3. 确认目标APP可调试

Q: 如何获取APP包名？
A: 使用命令: adb shell pm list packages | grep 关键词

Q: 报告在哪里？
A: 默认保存在 ./reports 目录下

=== 更多信息 ===
详细文档请查看 README.md
"""
    print(help_text)

def install_environment():
    """安装/更新环境"""
    print("🚀 安装/更新环境...")
    try:
        subprocess.run([sys.executable, "install.py"])
    except Exception as e:
        print(f"❌ 安装失败: {e}")

def main():
    """主函数"""
    while True:
        print_menu()
        
        try:
            choice = input("请选择操作 (1-7): ").strip()
            
            if choice == '1':
                launch_gui()
            elif choice == '2':
                detect_app()
            elif choice == '3':
                run_tests()
            elif choice == '4':
                check_quick_environment()
            elif choice == '5':
                show_help()
            elif choice == '6':
                install_environment()
            elif choice == '7':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入1-7")
            
            if choice != '7':
                input("\n按回车键继续...")
                print("\n" + "="*60 + "\n")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
