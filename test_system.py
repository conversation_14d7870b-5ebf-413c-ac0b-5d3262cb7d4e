#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
验证隐私合规检测工具的各个模块功能
"""

import os
import sys
import json
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import setup_logger, get_logger
from src.utils.report_generator import ReportGenerator
from src.detectors.privacy_policy_detector import PrivacyPolicyDetector
from src.detectors.permission_detector import PermissionDetector
from src.detectors.sdk_detector import SDKDetector
from src.detectors.user_rights_detector import UserRightsDetector

class TestPrivacyComplianceSystem(unittest.TestCase):
    """隐私合规检测系统测试"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = setup_logger("test", "DEBUG")
        self.mock_session = Mock()
        self.mock_device = Mock()
        self.test_config = {
            "detection_settings": {
                "timeout": 60,
                "max_violations_per_type": 10
            }
        }
    
    def test_logger_functionality(self):
        """测试日志功能"""
        logger = get_logger("test_logger")
        
        # 测试不同级别的日志
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, "test_logger")
    
    def test_report_generator(self):
        """测试报告生成器"""
        generator = ReportGenerator("./test_reports")
        
        # 模拟检测结果
        test_results = {
            'privacy_policy_violations': [
                {
                    'id': 1,
                    'type': 'missing_privacy_policy',
                    'description': '测试违规项目',
                    'severity': 'high',
                    'compliance_item': 1,
                    'timestamp': time.time(),
                    'evidence': {'test': 'data'}
                }
            ],
            'permission_violations': [],
            'sdk_violations': [],
            'user_rights_violations': []
        }
        
        test_app_info = {
            'package_name': 'com.test.app',
            'app_name': 'Test App',
            'version': '1.0.0',
            'device_info': 'Test Device',
            'detection_duration': 60.0
        }
        
        # 生成报告
        report_path = generator.generate_report(test_results, test_app_info)
        
        self.assertTrue(os.path.exists(report_path))
        self.assertTrue(report_path.endswith('.xlsx'))
        
        # 清理测试文件
        if os.path.exists(report_path):
            os.remove(report_path)
    
    def test_privacy_policy_detector(self):
        """测试隐私政策检测器"""
        detector = PrivacyPolicyDetector(self.mock_session, self.mock_device)
        
        # 模拟检测方法
        with patch.object(detector, '_search_privacy_policy', return_value=False):
            violations = detector.detect_all()
        
        self.assertIsInstance(violations, list)
        # 应该检测到缺少隐私政策的违规
        self.assertTrue(any(v.get('type') == 'missing_privacy_policy' for v in violations))
    
    def test_permission_detector(self):
        """测试权限检测器"""
        detector = PermissionDetector(self.mock_session, self.mock_device, self.test_config)
        
        # 模拟一些数据收集行为
        detector.data_collections = [
            {
                'type': 'device_id',
                'beforeConsent': True,
                'timestamp': time.time()
            }
        ]
        
        violations = detector.detect_all()
        
        self.assertIsInstance(violations, list)
        # 应该检测到提前收集的违规
        self.assertTrue(any(v.get('type') == 'premature_collection' for v in violations))
    
    def test_sdk_detector(self):
        """测试SDK检测器"""
        detector = SDKDetector(self.mock_session, self.mock_device, self.test_config)
        
        # 模拟网络请求数据
        test_requests = [
            {
                'url': 'https://google-analytics.com/collect',
                'data': 'imei=123456789012345&location=39.9042,116.4074',
                'timestamp': time.time()
            }
        ]
        
        detector.update_network_requests(test_requests)
        violations = detector.detect_all()
        
        self.assertIsInstance(violations, list)
    
    def test_user_rights_detector(self):
        """测试用户权利检测器"""
        detector = UserRightsDetector(self.mock_session, self.mock_device, self.test_config)
        
        violations = detector.detect_all()
        
        self.assertIsInstance(violations, list)
    
    def test_config_loading(self):
        """测试配置加载"""
        # 测试默认配置文件
        config_path = "./config/config.json"
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.assertIn('detection_settings', config)
            self.assertIn('frida_settings', config)
            self.assertIn('report_settings', config)
    
    def test_frida_scripts_exist(self):
        """测试Frida脚本文件存在性"""
        script_dir = "./src/frida_scripts"
        required_scripts = [
            'core_hooks.js',
            'ui_hooks.js',
            'sdk_hooks.js'
        ]
        
        for script in required_scripts:
            script_path = os.path.join(script_dir, script)
            self.assertTrue(os.path.exists(script_path), f"脚本文件不存在: {script}")
    
    def test_directory_structure(self):
        """测试目录结构"""
        required_dirs = [
            './src',
            './src/core',
            './src/detectors',
            './src/frida_scripts',
            './src/gui',
            './src/utils',
            './config'
        ]
        
        for directory in required_dirs:
            self.assertTrue(os.path.exists(directory), f"目录不存在: {directory}")
    
    def test_main_entry_points(self):
        """测试主入口点"""
        # 测试main.py存在
        self.assertTrue(os.path.exists('./main.py'))
        
        # 测试requirements.txt存在
        self.assertTrue(os.path.exists('./requirements.txt'))

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_mock_detection_flow(self):
        """测试模拟检测流程"""
        # 创建模拟的检测器
        from src.core.detector import PrivacyComplianceDetector
        
        # 由于需要真实的Frida环境，这里只测试初始化
        try:
            detector = PrivacyComplianceDetector(
                package_name="com.test.app",
                output_dir="./test_reports"
            )
            
            # 测试配置加载
            self.assertIsNotNone(detector.config)
            self.assertIsInstance(detector.detection_results, dict)
            
        except Exception as e:
            # 在没有Frida环境的情况下，这是预期的
            self.assertIn("frida", str(e).lower())

def run_performance_test():
    """运行性能测试"""
    print("=== 性能测试 ===")
    
    # 测试报告生成性能
    start_time = time.time()
    
    generator = ReportGenerator("./test_reports")
    
    # 生成大量测试数据
    large_results = {
        'privacy_policy_violations': [],
        'permission_violations': [],
        'sdk_violations': [],
        'user_rights_violations': []
    }
    
    # 添加100个违规项目
    for i in range(100):
        violation = {
            'id': i,
            'type': f'test_violation_{i}',
            'description': f'测试违规项目 {i}',
            'severity': 'medium',
            'compliance_item': (i % 30) + 1,
            'timestamp': time.time(),
            'evidence': {'data': f'test_data_{i}' * 100}  # 大量数据
        }
        large_results['privacy_policy_violations'].append(violation)
    
    app_info = {
        'package_name': 'com.performance.test',
        'app_name': 'Performance Test',
        'version': '1.0.0',
        'device_info': 'Test Device',
        'detection_duration': 300.0
    }
    
    report_path = generator.generate_report(large_results, app_info)
    
    end_time = time.time()
    generation_time = end_time - start_time
    
    print(f"报告生成时间: {generation_time:.2f}秒")
    print(f"报告文件大小: {os.path.getsize(report_path) / 1024:.2f} KB")
    
    # 清理测试文件
    if os.path.exists(report_path):
        os.remove(report_path)

def run_system_check():
    """运行系统检查"""
    print("=== 系统环境检查 ===")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ Python版本过低，需要3.7+")
    else:
        print("✅ Python版本符合要求")
    
    # 检查依赖包
    required_packages = [
        'frida', 'openpyxl', 'colorama', 'psutil'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
    
    # 检查Frida环境
    try:
        import frida
        devices = frida.enumerate_devices()
        print(f"✅ Frida环境正常，发现 {len(devices)} 个设备")
        for device in devices:
            print(f"   - {device.name} ({device.type})")
    except Exception as e:
        print(f"❌ Frida环境异常: {e}")
    
    # 检查目录权限
    test_dirs = ['./reports', './logs']
    for directory in test_dirs:
        try:
            os.makedirs(directory, exist_ok=True)
            test_file = os.path.join(directory, 'test.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            print(f"✅ {directory} 目录权限正常")
        except Exception as e:
            print(f"❌ {directory} 目录权限异常: {e}")

if __name__ == '__main__':
    print("APP隐私合规检测工具 - 系统测试")
    print("=" * 50)
    
    # 运行系统检查
    run_system_check()
    print()
    
    # 运行性能测试
    run_performance_test()
    print()
    
    # 运行单元测试
    print("=== 单元测试 ===")
    unittest.main(verbosity=2, exit=False)
    
    print("\n测试完成！")
