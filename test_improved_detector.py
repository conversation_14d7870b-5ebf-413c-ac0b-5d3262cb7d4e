#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的隐私合规检测器
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'src'))

try:
    from src.improved_detector import ImprovedPrivacyDetector
    from src.utils.logger import get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

logger = get_logger(__name__)

def test_ui_analyzer():
    """测试UI分析器"""
    print("🧪 测试UI分析器...")
    
    try:
        from src.utils.ui_analyzer import UIAnalyzer
        
        ui_analyzer = UIAnalyzer()
        ui_state = ui_analyzer.get_current_ui_state()
        
        if ui_state:
            print("✅ UI分析器工作正常")
            print(f"   获取到 {len(ui_state.get('text_elements', []))} 个文本元素")
            print(f"   获取到 {len(ui_state.get('clickable_elements', []))} 个可点击元素")
            
            # 测试隐私政策检测
            policy_detection = ui_analyzer.detect_privacy_policy_popup(ui_state)
            print(f"   隐私政策检测: {'检测到' if policy_detection['detected'] else '未检测到'}")
            print(f"   置信度: {policy_detection['confidence']}%")
            
            # 测试权限请求检测
            permission_detection = ui_analyzer.detect_permission_request(ui_state)
            print(f"   权限请求检测: {'检测到' if permission_detection['detected'] else '未检测到'}")
            if permission_detection['detected']:
                print(f"   权限类型: {', '.join(permission_detection['permission_types'])}")
        else:
            print("⚠️ UI分析器未获取到UI状态")
            
    except Exception as e:
        print(f"❌ UI分析器测试失败: {e}")

def test_interactive_detector():
    """测试交互式检测器"""
    print("\n🧪 测试交互式检测器...")
    
    try:
        from src.core.interactive_detector import InteractivePrivacyDetector
        from src.utils.ui_analyzer import UIAnalyzer
        
        ui_analyzer = UIAnalyzer()
        
        # 创建模拟的session和device
        class MockSession:
            def create_script(self, script): return MockScript()
            def detach(self): pass
        
        class MockScript:
            def on(self, event, handler): pass
            def load(self): pass
            def unload(self): pass
        
        class MockDevice:
            name = "测试设备"
            type = "usb"
        
        detector = InteractivePrivacyDetector(
            MockSession(), MockDevice(), {}, ui_analyzer
        )
        
        print("✅ 交互式检测器初始化成功")
        
        # 测试检测结果获取
        results = detector.get_detection_results()
        print(f"   检测结果结构: {list(results.keys())}")
        
    except Exception as e:
        print(f"❌ 交互式检测器测试失败: {e}")

def test_improved_detector_init():
    """测试改进检测器初始化"""
    print("\n🧪 测试改进检测器初始化...")
    
    try:
        detector = ImprovedPrivacyDetector(
            package_name="com.test.app",
            interactive_mode=True
        )
        
        print("✅ 改进检测器初始化成功")
        print(f"   包名: {detector.package_name}")
        print(f"   交互模式: {detector.interactive_mode}")
        print(f"   输出目录: {detector.output_dir}")
        
    except Exception as e:
        print(f"❌ 改进检测器初始化失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试改进的隐私合规检测器")
    print("=" * 60)
    
    # 测试各个组件
    test_ui_analyzer()
    test_interactive_detector()
    test_improved_detector_init()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    
    # 提供使用示例
    print("\n📖 使用示例:")
    print("python src/improved_detector.py com.example.app")
    print("python src/improved_detector.py com.example.app -d device_id")
    print("python src/improved_detector.py com.example.app --no-interactive")

if __name__ == '__main__':
    main()
