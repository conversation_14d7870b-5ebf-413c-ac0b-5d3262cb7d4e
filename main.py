#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APP隐私合规检测工具 - 主程序
基于Frida的动态分析工具，检测30项隐私合规问题

使用方法:
  GUI模式:    python main.py --gui
  命令行模式: python main.py --package com.example.app
  查看帮助:   python main.py --help
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_environment():
    """检查运行环境"""
    try:
        import frida
        return True
    except ImportError:
        print("❌ 错误: 未安装Frida")
        print("请运行: pip install frida")
        return False

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                APP隐私合规检测工具 v1.0                       ║
║                Privacy Compliance Detector                   ║
║                                                              ║
║  支持30项隐私合规检测 | 基于Frida动态分析 | 生成详细报告       ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """主函数"""
    print_banner()

    parser = argparse.ArgumentParser(
        description='APP隐私合规检测工具 - 基于Frida的动态分析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  启动GUI界面:
    python main.py --gui

  检测指定APP:
    python main.py --package com.example.app

  指定设备和输出目录:
    python main.py --package com.example.app --device emulator-5554 --output ./my_reports

  使用自定义配置:
    python main.py --package com.example.app --config ./my_config.json

注意事项:
  - 确保Android设备已连接并启用USB调试
  - 确保Frida服务端已在设备上运行
  - 目标APP需要可调试或设备已Root
        """
    )

    parser.add_argument('--gui', action='store_true',
                       help='启动GUI界面')
    parser.add_argument('--package', '-p',
                       help='目标APP包名 (例如: com.example.app)')
    parser.add_argument('--device', '-d',
                       help='设备ID (可选，默认自动检测)')
    parser.add_argument('--output', '-o',
                       help='输出报告目录', default='./reports')
    parser.add_argument('--config', '-c',
                       help='配置文件路径', default='./config/config.json')
    parser.add_argument('--version', action='version', version='%(prog)s 1.0.0')
    parser.add_argument('--test', action='store_true',
                       help='运行系统测试')

    args = parser.parse_args()

    # 检查环境
    if not check_environment():
        sys.exit(1)

    # 运行测试
    if args.test:
        print("🧪 运行系统测试...")
        try:
            import test_system
            test_system.run_system_check()
        except ImportError:
            print("❌ 测试模块未找到")
        return

    # 导入模块
    try:
        from src.gui.main_window import PrivacyComplianceGUI
        from src.core.detector import PrivacyComplianceDetector
        from src.utils.logger import setup_logger
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保所有依赖已正确安装")
        sys.exit(1)

    # 设置日志
    logger = setup_logger()

    if args.gui:
        # 启动GUI界面
        logger.info("启动GUI界面")
        try:
            app = PrivacyComplianceGUI()
            app.run()
        except Exception as e:
            logger.error(f"GUI启动失败: {e}")
            print(f"❌ GUI启动失败: {e}")
            sys.exit(1)
    else:
        # 命令行模式
        if not args.package:
            print("❌ 错误: 命令行模式需要指定包名")
            print("使用 --package 参数指定目标APP包名")
            print("或使用 --gui 启动图形界面")
            print("使用 --help 查看详细帮助")
            sys.exit(1)

        print(f"🎯 目标APP: {args.package}")
        print(f"📁 输出目录: {args.output}")

        logger.info(f"开始检测APP: {args.package}")

        try:
            detector = PrivacyComplianceDetector(
                package_name=args.package,
                device_id=args.device,
                output_dir=args.output,
                config_path=args.config
            )

            # 执行检测
            print("🚀 开始隐私合规检测...")
            results = detector.run_detection()

            if results.get('success'):
                print("✅ 检测完成！")
                print(f"📊 发现 {results.get('violations_count', 0)} 个违规项")
                print(f"⏱️  检测耗时: {results.get('detection_duration', 0):.1f}秒")
                print(f"📄 报告路径: {results.get('report_path', '')}")
                logger.info(f"检测成功完成: {results}")
            else:
                error_msg = results.get('error', '未知错误')
                print(f"❌ 检测失败: {error_msg}")
                logger.error(f"检测失败: {error_msg}")
                sys.exit(1)

        except KeyboardInterrupt:
            print("\n⚠️  检测被用户中断")
            logger.info("检测被用户中断")
        except Exception as e:
            print(f"❌ 检测过程中发生错误: {e}")
            logger.error(f"检测过程中发生错误: {e}")
            sys.exit(1)

if __name__ == '__main__':
    main()
